"""
Backtesting Engine Module
Core backtesting logic for comprehensive rule evaluation
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Drawdown calculation functions
def calculate_drawdown_metrics(trades, initial_capital):
    """Calculate comprehensive drawdown metrics from trades"""
    if not trades:
        return {
            'max_drawdown_pct': 0,
            'max_drawdown_duration': 0,
            'relative_drawdown_pct': 0,
            'equity_curve': [initial_capital],
            'drawdown_curve': [0],
            'underwater_curve': [0],
            'recovery_time': 0,
            'drawdown_periods': []
        }

    # Create equity curve
    equity_curve = [initial_capital]
    running_capital = initial_capital

    for trade in trades:
        running_capital += trade['pnl']
        equity_curve.append(running_capital)

    # Calculate drawdown curve
    peak = initial_capital
    drawdown_curve = []
    underwater_curve = []
    drawdown_periods = []
    current_drawdown_start = None
    max_drawdown_pct = 0
    max_drawdown_duration = 0

    for i, equity in enumerate(equity_curve):
        if equity > peak:
            peak = equity
            if current_drawdown_start is not None:
                # End of drawdown period
                drawdown_periods.append({
                    'start_idx': current_drawdown_start,
                    'end_idx': i - 1,
                    'duration': i - 1 - current_drawdown_start,
                    'max_dd_pct': max_drawdown_pct
                })
                current_drawdown_start = None

        # Calculate current drawdown
        drawdown_pct = ((peak - equity) / peak) * 100 if peak > 0 else 0
        drawdown_curve.append(drawdown_pct)
        underwater_curve.append(equity - peak)

        # Track maximum drawdown
        if drawdown_pct > max_drawdown_pct:
            max_drawdown_pct = drawdown_pct

        # Track drawdown periods
        if drawdown_pct > 0 and current_drawdown_start is None:
            current_drawdown_start = i

    # Handle ongoing drawdown at end
    if current_drawdown_start is not None:
        drawdown_periods.append({
            'start_idx': current_drawdown_start,
            'end_idx': len(equity_curve) - 1,
            'duration': len(equity_curve) - 1 - current_drawdown_start,
            'max_dd_pct': max_drawdown_pct
        })

    # Calculate maximum drawdown duration
    if drawdown_periods:
        max_drawdown_duration = max(period['duration'] for period in drawdown_periods)

    # Calculate relative drawdown (max drawdown relative to initial capital)
    relative_drawdown_pct = ((initial_capital - min(equity_curve)) / initial_capital) * 100 if initial_capital > 0 else 0

    # Calculate recovery time (time to recover from max drawdown)
    recovery_time = 0
    if drawdown_periods:
        max_dd_period = max(drawdown_periods, key=lambda x: x['max_dd_pct'])
        recovery_time = max_dd_period['duration']

    return {
        'max_drawdown_pct': max_drawdown_pct,
        'max_drawdown_duration': max_drawdown_duration,
        'relative_drawdown_pct': relative_drawdown_pct,
        'equity_curve': equity_curve,
        'drawdown_curve': drawdown_curve,
        'underwater_curve': underwater_curve,
        'recovery_time': recovery_time,
        'drawdown_periods': drawdown_periods
    }

# Global worker function for multiprocessing (must be at module level for pickling)
def process_rule_batch_worker(args):
    """Worker function to process a batch of rules in parallel"""
    rule_batch, worker_data = args

    # Extract data
    df_data = worker_data['df_data']
    start_idx = worker_data['start_idx']
    end_idx = worker_data['end_idx']
    config = worker_data['config']

    # Initialize results for this batch
    batch_results = {}

    # Process each rule in this batch
    for rule_name, rule_func in rule_batch:
        try:
            # Run isolated backtest for this rule
            result = run_isolated_rule_backtest_worker(
                rule_name, rule_func, df_data, config
            )
            batch_results[rule_name] = result

        except Exception as e:
            # Handle errors gracefully
            batch_results[rule_name] = {
                'rule_name': rule_name,
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'neutral_trades': 0,
                'win_rate': 0,
                'total_return_pct': 0,
                'profit_factor': 0,
                'total_profits': 0,
                'total_losses': 0,
                'final_capital': config['INITIAL_CAPITAL'],
                'total_signals': 0,
                'signal_to_trade_ratio': 0,
                'avg_trade_duration_minutes': 0,
                'trades': [],
                'error': str(e)
            }

    return batch_results

# Enhanced worker function with progress tracking
def process_rule_batch_worker_with_progress(args):
    """Enhanced worker function with real-time progress tracking"""
    rule_batch, worker_data = args

    # Extract data
    df_data = worker_data['df_data']
    config = worker_data['config']
    batch_id = worker_data.get('batch_id', 0)
    progress_dict = worker_data.get('progress_dict')

    # Initialize results for this batch
    batch_results = {}

    # Process each rule in this batch with progress updates
    for i, (rule_name, rule_func) in enumerate(rule_batch):
        try:
            # Run isolated backtest for this rule
            result = run_isolated_rule_backtest_worker(
                rule_name, rule_func, df_data, config
            )
            batch_results[rule_name] = result

            # Update progress if tracking is available
            if progress_dict is not None:
                try:
                    progress_dict['completed_rules'] = progress_dict.get('completed_rules', 0) + 1
                except:
                    pass  # Ignore progress update errors

        except Exception as e:
            # Handle errors gracefully
            batch_results[rule_name] = {
                'rule_name': rule_name,
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'neutral_trades': 0,
                'win_rate': 0,
                'total_return_pct': 0,
                'profit_factor': 0,
                'total_profits': 0,
                'total_losses': 0,
                'final_capital': config['INITIAL_CAPITAL'],
                'total_signals': 0,
                'signal_to_trade_ratio': 0,
                'avg_trade_duration_minutes': 0,
                'trades': [],
                'error': str(e)
            }

    # Mark batch as completed
    if progress_dict is not None:
        try:
            progress_dict['completed_batches'] = progress_dict.get('completed_batches', 0) + 1
        except:
            pass

    return batch_results

def check_global_market_filters_worker(df_data, idx, config):
    """Check global market filters for worker functions"""
    if not config.get('ENABLE_GLOBAL_MARKET_FILTERS', True):
        return True

    # Check EMA200 filter - price must be above EMA200
    if config.get('GLOBAL_EMA200_FILTER', True):
        if 'EMA_200' in df_data.columns:
            current_price = df_data['close'].iloc[idx]
            ema200_value = df_data['EMA_200'].iloc[idx]
            if not pd.isna(ema200_value) and current_price < ema200_value:
                return False

    # Check RSI overbought filter - RSI must be below threshold
    if config.get('GLOBAL_RSI_OVERBOUGHT_FILTER', True):
        if 'RSI' in df_data.columns:
            rsi_value = df_data['RSI'].iloc[idx]
            threshold = config.get('GLOBAL_RSI_OVERBOUGHT_THRESHOLD', 75)
            if not pd.isna(rsi_value) and rsi_value > threshold:
                return False

    # Check daily drop filter - block trades when daily drop >= threshold
    if config.get('ENABLE_DAILY_DROP_FILTER', True):
        daily_drop = calculate_daily_drop_worker(df_data, idx)
        threshold = config.get('DAILY_DROP_THRESHOLD', 3.0)
        if daily_drop >= threshold:
            return False

    return True

def calculate_daily_drop_worker(df_data, idx):
    """Calculate the daily drop percentage for worker processes"""
    try:
        if idx < 1:
            return 0.0

        # Get current price
        current_price = df_data['close'].iloc[idx]

        # Find the daily high (look back up to 1440 minutes = 24 hours for 1-minute data)
        lookback_period = min(1440, idx + 1)  # Don't go beyond available data
        start_idx = max(0, idx - lookback_period + 1)

        # Get the highest price in the current day
        daily_high = df_data['high'].iloc[start_idx:idx + 1].max()

        if daily_high <= 0:
            return 0.0

        # Calculate drop percentage from daily high
        daily_drop = ((daily_high - current_price) / daily_high) * 100

        return max(0.0, daily_drop)  # Return 0 if price is above daily high

    except Exception:
        return 0.0  # Return 0 if calculation fails

def run_isolated_rule_backtest_worker(rule_name, rule_func, df_data, config):
    """Run isolated backtest for a single rule - optimized for multiprocessing with multiple positions"""

    # Initialize trading state
    capital = config['INITIAL_CAPITAL']
    position = None  # Keep for backward compatibility
    positions = []   # NEW: Support multiple concurrent positions
    trades = []
    signal_count = 0

    # Get max concurrent trades from config
    max_concurrent_trades = config.get('MAX_CONCURRENT_TRADES', 1)

    # Pre-extract price data for maximum speed
    close_prices = df_data['close'].values

    # Cache configuration values
    stop_loss_pct = config['stop_loss_pct']
    take_profit_pct = config['take_profit_pct']
    position_size_pct = config['POSITION_SIZE_PCT']

    # Get the actual starting index from the DataFrame
    if 'original_index' in df_data.columns:
        # Use the original index mapping for correct rule evaluation
        original_indices = df_data['original_index'].values
    else:
        # Fallback to calculated indices
        start_idx = df_data.index[0] if len(df_data) > 0 else 0
        original_indices = [start_idx + i for i in range(len(df_data))]

    # Ultra-fast backtest loop with multiple positions support
    for i in range(len(df_data)):
        current_price = close_prices[i]
        actual_idx = original_indices[i]  # OPTIMIZED: Use pre-calculated original indices

        # Check for exit conditions for all positions
        positions_to_close = []
        for pos in positions:
            entry_price = pos['entry_price']
            current_return = (current_price / entry_price - 1) * 100

            # Check stop loss or take profit
            if current_return <= -stop_loss_pct or current_return >= take_profit_pct:
                positions_to_close.append(pos)

                # Execute exit
                exit_value = pos['quantity'] * current_price
                pnl = exit_value - pos['position_value']
                capital += pnl

                exit_reason = f"Stop Loss ({current_return:.2f}%)" if current_return <= -stop_loss_pct else f"Take Profit ({current_return:.2f}%)"

                trades.append({
                    'entry_time': pos['entry_time'],
                    'exit_time': f"Index_{actual_idx}",
                    'entry_price': entry_price,
                    'exit_price': current_price,
                    'quantity': pos['quantity'],
                    'position_value': pos['position_value'],
                    'pnl': pnl,
                    'pnl_pct': current_return,
                    'rule_triggered': rule_name,
                    'exit_reason': exit_reason,
                    'entry_idx': pos['entry_idx'],
                    'exit_idx': actual_idx,
                    'duration_minutes': actual_idx - pos['entry_idx']
                })

        # Remove closed positions
        for pos in positions_to_close:
            positions.remove(pos)

        # Backward compatibility: update single position variable
        position = positions[0] if positions else None

        # Check for buy signal if we can open more positions
        if len(positions) < max_concurrent_trades:
            try:
                # Apply global market filters before checking rule
                if check_global_market_filters_worker(df_data, i, config) and rule_func(actual_idx):
                    signal_count += 1

                    # Calculate position size (divide available capital by max positions for risk management)
                    available_capital = capital / max_concurrent_trades
                    position_value = available_capital * position_size_pct
                    quantity = position_value / current_price

                    new_position = {
                        'entry_time': f"Index_{actual_idx}",
                        'entry_price': current_price,
                        'quantity': quantity,
                        'position_value': position_value,
                        'entry_idx': actual_idx
                    }

                    positions.append(new_position)
                    # Update backward compatibility variable
                    position = new_position
            except:
                # Skip if rule evaluation fails
                pass

    # Close any remaining positions at end
    for remaining_position in positions:
        current_price = close_prices[-1]
        exit_value = remaining_position['quantity'] * current_price
        pnl = exit_value - remaining_position['position_value']
        capital += pnl
        current_return = (current_price / remaining_position['entry_price'] - 1) * 100

        trades.append({
            'entry_time': remaining_position['entry_time'],
            'exit_time': f"Index_{len(df_data) - 1}",
            'entry_price': remaining_position['entry_price'],
            'exit_price': current_price,
            'quantity': remaining_position['quantity'],
            'position_value': remaining_position['position_value'],
            'pnl': pnl,
            'pnl_pct': current_return,
            'rule_triggered': rule_name,
            'exit_reason': "End of backtest",
            'entry_idx': remaining_position['entry_idx'],
            'exit_idx': len(df_data) - 1,
            'duration_minutes': (len(df_data) - 1) - remaining_position['entry_idx']
        })

    # Calculate performance metrics
    total_trades = len(trades)
    winning_trades = len([t for t in trades if t['pnl'] > 0])
    losing_trades = len([t for t in trades if t['pnl'] < 0])
    neutral_trades = len([t for t in trades if t['pnl'] == 0])

    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
    total_return_pct = (capital / config['INITIAL_CAPITAL'] - 1) * 100

    total_profits = sum([t['pnl'] for t in trades if t['pnl'] > 0])
    total_losses = abs(sum([t['pnl'] for t in trades if t['pnl'] < 0]))
    profit_factor = (total_profits / total_losses) if total_losses > 0 else float('inf')

    avg_trade_duration = sum([t['duration_minutes'] for t in trades]) / total_trades if total_trades > 0 else 0

    # Calculate drawdown metrics
    drawdown_metrics = calculate_drawdown_metrics(trades, config['INITIAL_CAPITAL'])

    return {
        'rule_name': rule_name,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'neutral_trades': neutral_trades,
        'win_rate': win_rate,
        'total_return_pct': total_return_pct,
        'profit_factor': profit_factor,
        'total_profits': total_profits,
        'total_losses': total_losses,
        'final_capital': capital,
        'total_signals': signal_count,
        'signal_to_trade_ratio': total_trades / signal_count if signal_count > 0 else 0,
        'avg_trade_duration_minutes': avg_trade_duration,
        'trades': trades,
        # Enhanced drawdown metrics
        'max_drawdown_pct': drawdown_metrics['max_drawdown_pct'],
        'max_drawdown_duration': drawdown_metrics['max_drawdown_duration'],
        'relative_drawdown_pct': drawdown_metrics['relative_drawdown_pct'],
        'recovery_time': drawdown_metrics['recovery_time'],
        'equity_curve': drawdown_metrics['equity_curve'],
        'drawdown_curve': drawdown_metrics['drawdown_curve'],
        'underwater_curve': drawdown_metrics['underwater_curve'],
        'drawdown_periods': drawdown_metrics['drawdown_periods']
    }

from config import Config
from indicators import TechnicalIndicators
from original_rules import OriginalBuyRules, OriginalSellRules
from professional_rules import ProfessionalBuyRules, ProfessionalSellRules, AcademicBuyRules
from ai_generated_rules import AIGeneratedBuyRules


class BacktestingEngine:
    """Main backtesting engine for comprehensive rule evaluation"""
    
    def __init__(self, config=None):
        """Initialize backtesting engine"""
        self.config = config or Config()
        
        # Trading state
        self.initial_capital = self.config.INITIAL_CAPITAL
        self.current_capital = self.initial_capital
        self.position = None
        
        # Results storage
        self.all_signals = []  # All signals generated
        self.all_trades = []   # All completed trades
        self.rule_performance = {}  # Performance by rule
        
        # Load and prepare data
        self._load_and_prepare_data()
        
        # Initialize all rule systems
        self._initialize_rule_systems()
        
        print(f"Backtesting Engine initialized")
        print(f"Dataset size: {len(self.df):,} candles")
        print(f"Initial capital: ${self.initial_capital:,}")
    
    def _load_and_prepare_data(self):
        """Load data and calculate all technical indicators"""
        print(f"Loading data from {self.config.DATA_FILE}...")
        
        # Load data
        self.df = pd.read_csv(self.config.DATA_FILE)
        
        # Limit dataset size if configured
        if self.config.get_dataset_size():
            original_size = len(self.df)
            self.df = self.df.head(self.config.get_dataset_size())
            print(f"Dataset limited to {len(self.df):,} candles (from {original_size:,})")
        
        # Ensure required columns exist
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in self.df.columns:
                raise ValueError(f"Required column '{col}' not found in data")
        
        # Calculate all technical indicators
        print("Calculating technical indicators...")
        indicators = TechnicalIndicators(self.df)
        self.df = indicators.calculate_all_indicators()
        
        print(f"Technical indicators calculated successfully")
    
    def _initialize_rule_systems(self):
        """Initialize all rule systems"""
        print("Initializing rule systems...")

        # Check if we should use only optimized rules
        use_optimized_only = getattr(self.config, 'USE_OPTIMIZED_RULES_ONLY', False)

        if use_optimized_only:
            print("🚀 Using ONLY optimized rules from selected_buy_rules_optimized.py")

            # Load optimized rules
            try:
                from selected_buy_rules_optimized import SelectedBuyRulesOptimized
                self.optimized_buy_rules = SelectedBuyRulesOptimized(self.df)
                print("✅ Optimized buy rules loaded successfully")

                # Set all rules to only the optimized ones
                self.all_buy_rules = self.optimized_buy_rules.get_all_rules()
                print(f"✅ Loaded {len(self.all_buy_rules)} optimized buy rules")

                # Still need sell rules for exits (if not disabled)
                from original_rules import OriginalSellRules
                self.original_sell_rules = OriginalSellRules(self.df)
                self.all_sell_rules = []

                # Add sell rules if not disabled
                if not self.config.DISABLE_ALL_SELL_RULES:
                    self.all_sell_rules.extend(self.original_sell_rules.get_all_rules())

                print(f"✅ Optimized rules initialization complete!")
                print(f"   - Buy rules: {len(self.all_buy_rules)}")
                print(f"   - Sell rules: {len(self.all_sell_rules)}")
                return

            except ImportError as e:
                print(f"❌ Could not load optimized rules: {e}")
                print("   Falling back to standard rule loading...")

        # Standard rule loading (original behavior)
        print("📚 Loading standard rule sets...")

        # Original rules
        self.original_buy_rules = OriginalBuyRules(self.df)

        # Expanded professional rules
        try:
            from expanded_buy_rules import ExpandedBuyRules
            from github_strategy_rules import GitHubStrategyRules
            from research_based_rules import ResearchBasedRules
            from external_source_rules import ExternalSourceBuyRules

            self.expanded_buy_rules = ExpandedBuyRules(self.df, self._create_indicators_dict())
            self.github_strategy_rules = GitHubStrategyRules(self.df, self._create_indicators_dict())
            self.research_based_rules = ResearchBasedRules(self.df, self._create_indicators_dict())
            self.external_source_rules = ExternalSourceBuyRules(self.df, self._create_indicators_dict())
            print("✅ Enhanced rule sets loaded successfully")
        except ImportError as e:
            print(f"⚠️ Could not load enhanced rule sets: {e}")
            self.expanded_buy_rules = None
            self.github_strategy_rules = None
            self.research_based_rules = None
            self.external_source_rules = None
        self.original_sell_rules = OriginalSellRules(self.df)

        # Professional rules
        self.professional_buy_rules = ProfessionalBuyRules(self.df)
        self.professional_sell_rules = ProfessionalSellRules(self.df)

        # Academic rules
        self.academic_buy_rules = AcademicBuyRules(self.df)

        # AI-generated rules
        self.ai_buy_rules = AIGeneratedBuyRules(self.df)

        # Enhanced sell rules
        try:
            from enhanced_sell_rules import EnhancedSellRules
            self.enhanced_sell_rules = EnhancedSellRules(self.df)
            print("✅ Enhanced sell rules loaded successfully")
        except Exception as e:
            print(f"⚠️ Could not load enhanced sell rules: {e}")
            self.enhanced_sell_rules = None

        # New buy rules from NEWBUYRULES2.md
        try:
            from newbuyrules2 import NewBuyRules2
            self.new_buy_rules2 = NewBuyRules2(self.df)
            print("✅ New buy rules from NEWBUYRULES2.md loaded successfully")
        except Exception as e:
            print(f"⚠️ Could not load new buy rules: {e}")
            self.new_buy_rules2 = None

        # Collect all rules
        self.all_buy_rules = []
        self.all_sell_rules = []

        # Add buy rules by category
        if 'ORIGINAL' in self.config.ENABLED_RULE_CATEGORIES:
            self.all_buy_rules.extend(self.original_buy_rules.get_all_rules())

        if 'PROFESSIONAL' in self.config.ENABLED_RULE_CATEGORIES:
            self.all_buy_rules.extend(self.professional_buy_rules.get_all_rules())

        if 'ACADEMIC' in self.config.ENABLED_RULE_CATEGORIES:
            self.all_buy_rules.extend(self.academic_buy_rules.get_all_rules())
        
        if 'AI_GENERATED' in self.config.ENABLED_RULE_CATEGORIES:
            self.all_buy_rules.extend(self.ai_buy_rules.get_all_rules())

        # Add expanded professional rules
        if self.expanded_buy_rules and 'EXPANDED' in self.config.ENABLED_RULE_CATEGORIES:
            self.all_buy_rules.extend(self.expanded_buy_rules.get_all_rules())
            print(f"✅ Added {len(self.expanded_buy_rules.get_all_rules())} expanded buy rules")

        # Add GitHub strategy rules
        if self.github_strategy_rules:
            self.all_buy_rules.extend(self.github_strategy_rules.get_all_rules())
            print(f"✅ Added {len(self.github_strategy_rules.get_all_rules())} GitHub strategy rules")

        # Add research-based rules
        if self.research_based_rules:
            self.all_buy_rules.extend(self.research_based_rules.get_all_rules())
            print(f"✅ Added {len(self.research_based_rules.get_all_rules())} research-based rules")

        # Add external source rules
        if self.external_source_rules:
            self.all_buy_rules.extend(self.external_source_rules.get_all_rules())
            print(f"✅ Added {len(self.external_source_rules.get_all_rules())} external source rules")

        # Add new buy rules from NEWBUYRULES2.md
        if self.new_buy_rules2:
            self.all_buy_rules.extend(self.new_buy_rules2.get_all_rules())
            print(f"✅ Added {len(self.new_buy_rules2.get_all_rules())} new buy rules from NEWBUYRULES2.md")

        # Add sell rules by category
        if 'ORIGINAL' in self.config.ENABLED_RULE_CATEGORIES:
            self.all_sell_rules.extend(self.original_sell_rules.get_all_rules())

        if 'PROFESSIONAL' in self.config.ENABLED_RULE_CATEGORIES:
            self.all_sell_rules.extend(self.professional_sell_rules.get_all_rules())

        # Add enhanced sell rules (always enabled for better performance)
        if self.enhanced_sell_rules:
            self.all_sell_rules.extend(self.enhanced_sell_rules.get_all_rules())
            print(f"✅ Added {len(self.enhanced_sell_rules.get_all_rules())} enhanced sell rules")
        
        # Filter disabled rules
        self.all_buy_rules = [
            (name, func) for name, func in self.all_buy_rules
            if name not in self.config.DISABLED_RULES
        ]
        
        self.all_sell_rules = [
            (name, func) for name, func in self.all_sell_rules
            if name not in self.config.DISABLED_RULES
        ]
        
        print(f"Initialized {len(self.all_buy_rules)} buy rules")
        print(f"Initialized {len(self.all_sell_rules)} sell rules")

    def _create_indicators_dict(self):
        """Create indicators dictionary for expanded rules"""
        return {
            # Moving Averages
            'sma_5': self.df.get('SMA_5', pd.Series(index=self.df.index)),
            'sma_10': self.df.get('SMA_10', pd.Series(index=self.df.index)),
            'sma_20': self.df.get('SMA_20', pd.Series(index=self.df.index)),
            'sma_50': self.df.get('SMA_50', pd.Series(index=self.df.index)),
            'ema_20': self.df.get('EMA_20', pd.Series(index=self.df.index)),

            # Oscillators
            'rsi': self.df.get('RSI', pd.Series(index=self.df.index)),
            'macd': self.df.get('MACD', pd.Series(index=self.df.index)),
            'macd_signal': self.df.get('MACD_signal', pd.Series(index=self.df.index)),
            'stoch_k': self.df.get('STOCH_K', pd.Series(index=self.df.index)),
            'stoch_d': self.df.get('STOCH_D', pd.Series(index=self.df.index)),
            'williams_r': self.df.get('Williams_R', pd.Series(index=self.df.index)),

            # Volatility
            'bb_upper': self.df.get('BB_upper', pd.Series(index=self.df.index)),
            'bb_middle': self.df.get('BB_middle', pd.Series(index=self.df.index)),
            'bb_lower': self.df.get('BB_lower', pd.Series(index=self.df.index)),
            'atr': self.df.get('ATR', pd.Series(index=self.df.index)),

            # Trend
            'adx': self.df.get('ADX', pd.Series(index=self.df.index)),
            'di_plus': self.df.get('PLUS_DI', pd.Series(index=self.df.index)),
            'di_minus': self.df.get('MINUS_DI', pd.Series(index=self.df.index)),
        }
    
    def run_comprehensive_backtest(self):
        """Run comprehensive backtest with SL/TP optimization"""

        print("\n" + "=" * 60)
        print("STARTING ADVANCED UNIFIED EVALUATION WITH SL/TP OPTIMIZATION")
        print("=" * 60)

        # Get backtest range
        start_idx, end_idx = self.config.get_backtest_range(len(self.df))

        print(f"Backtest range: {start_idx:,} to {end_idx:,}")
        print(f"Total candles to process: {end_idx - start_idx:,}")
        print(f"Testing {len(self.all_buy_rules)} buy rules and {len(self.all_sell_rules)} sell rules")
        print()

        # Run SL/TP optimization if enabled
        if self.config.ENABLE_SLTP_OPTIMIZATION:
            print("🔧 Running SL/TP optimization across multiple configurations...")
            optimization_results = self._run_sltp_optimization(start_idx, end_idx)
        else:
            print("Running single configuration unified evaluation...")
            optimization_results = {
                'Current_Default': self._run_unified_backtest(start_idx, end_idx,
                                                            self.config.STOP_LOSS_PCT,
                                                            self.config.TAKE_PROFIT_PCT)
            }

        # Also run individual tests for comparison (optional)
        individual_results = {}
        if self.config.COMPARE_WITH_INDIVIDUAL:
            print("Running individual rule tests for comparison...")
            individual_results = self._test_buy_rules_individually(start_idx, end_idx)

        # Combine results
        all_results = {
            'optimization_results': optimization_results,
            'individual_results': individual_results,
            'backtest_info': {
                'start_idx': start_idx,
                'end_idx': end_idx,
                'total_candles': end_idx - start_idx,
                'dataset_size': len(self.df),
                'initial_capital': self.initial_capital,
                'evaluation_method': 'UNIFIED_WITH_OPTIMIZATION',
                'sltp_configs_tested': len(optimization_results),
            }
        }

        print("\n" + "=" * 60)
        print("ADVANCED UNIFIED BACKTEST COMPLETED")
        print("=" * 60)

        return all_results

    def _run_sltp_optimization(self, start_idx, end_idx):
        """Run backtest across multiple SL/TP configurations"""

        optimization_results = {}

        for config in self.config.RISK_CONFIGS:
            config_name = config['name']
            stop_loss = config['stop_loss']
            take_profit = config['take_profit']

            print(f"  Testing {config_name}: SL={stop_loss}%, TP={take_profit}% (1:{take_profit/stop_loss:.1f})")

            # Run unified backtest with this configuration
            result = self._run_unified_backtest(start_idx, end_idx, stop_loss, take_profit)
            result['config'] = config
            optimization_results[config_name] = result

            # Print quick summary
            total_return = result['total_return_pct']
            total_trades = result['total_trades']
            print(f"    Result: {total_return:+.2f}% return, {total_trades} trades")

        # Find best configuration
        best_config = max(optimization_results.keys(),
                         key=lambda k: optimization_results[k]['total_return_pct'])
        best_return = optimization_results[best_config]['total_return_pct']

        print(f"\n🏆 Best SL/TP Configuration: {best_config} ({best_return:+.2f}% return)")

        return optimization_results

    def _run_parallel_backtests(self, start_idx, end_idx):
        """Run backtests in parallel using TRUE multiprocessing"""
        import multiprocessing as mp
        from multiprocessing import Pool
        import os
        import pickle

        # MAXIMUM SPEED: Use more workers than cores for I/O bound rule evaluation
        cpu_count = os.cpu_count()
        max_workers = getattr(self.config, 'MAX_WORKERS', 15)  # Aggressive worker count

        print(f"   💻 Detected {cpu_count} CPU cores, using {max_workers} workers")
        print(f"   🚀 TRUE MULTIPROCESSING: Each core will process rules independently")

        # FIXED: Always create 1 rule per batch for optimal memory usage and parallelism
        # This ensures consistent batch count regardless of MAX_WORKERS setting
        rules_per_batch = 1  # Always 1 rule per batch for maximum parallelism and memory efficiency

        rule_batches = []
        for i in range(0, len(self.all_buy_rules), rules_per_batch):
            batch = self.all_buy_rules[i:i + rules_per_batch]
            if batch:  # Only add non-empty batches
                rule_batches.append(batch)

        print(f"   🔥 MEMORY OPTIMIZED: {len(rule_batches)} batches (1 rule each) - prevents memory overflow")

        # OPTIMIZATION: Prepare shared data with memory optimization
        # Use only essential columns to reduce memory footprint
        essential_columns = ['close', 'high', 'low', 'open', 'volume']

        # Add global filter columns if they exist and filters are enabled
        if getattr(self.config, 'ENABLE_GLOBAL_MARKET_FILTERS', True):
            if 'EMA_200' in self.df.columns:
                essential_columns.append('EMA_200')
            if 'RSI' in self.df.columns:
                essential_columns.append('RSI')

        df_slice = self.df[essential_columns].iloc[start_idx:end_idx].copy()

        # Reset index to ensure proper indexing in workers
        df_slice = df_slice.reset_index(drop=False)
        df_slice['original_index'] = df_slice.index + start_idx

        worker_data = {
            'df_data': df_slice,
            'start_idx': start_idx,
            'end_idx': end_idx,
            'config': {
                'INITIAL_CAPITAL': self.config.INITIAL_CAPITAL,
                'POSITION_SIZE_PCT': self.config.POSITION_SIZE_PCT,
                'stop_loss_pct': self.current_stop_loss_pct,
                'take_profit_pct': self.current_take_profit_pct,
                'MAX_CONCURRENT_TRADES': getattr(self.config, 'MAX_CONCURRENT_TRADES', 1),  # Add multiple positions support
                # Add global filter configuration
                'ENABLE_GLOBAL_MARKET_FILTERS': getattr(self.config, 'ENABLE_GLOBAL_MARKET_FILTERS', True),
                'GLOBAL_EMA200_FILTER': getattr(self.config, 'GLOBAL_EMA200_FILTER', True),
                'GLOBAL_RSI_OVERBOUGHT_FILTER': getattr(self.config, 'GLOBAL_RSI_OVERBOUGHT_FILTER', True),
                'GLOBAL_RSI_OVERBOUGHT_THRESHOLD': getattr(self.config, 'GLOBAL_RSI_OVERBOUGHT_THRESHOLD', 75),
                # Add daily drop filter configuration
                'ENABLE_DAILY_DROP_FILTER': getattr(self.config, 'ENABLE_DAILY_DROP_FILTER', True),
                'DAILY_DROP_THRESHOLD': getattr(self.config, 'DAILY_DROP_THRESHOLD', 3.0)
            }
        }

        # Create worker arguments
        worker_args = []
        for batch in rule_batches:
            if batch:  # Only add non-empty batches
                worker_args.append((batch, worker_data))

        print(f"   📦 Created {len(worker_args)} worker batches")

        # OPTIMIZATION 4: Advanced Progress Monitoring with Real-time Updates
        try:
            import time
            from multiprocessing import Manager

            print(f"   🚀 EXTREME SPEED MODE: Starting {len(worker_args)} parallel workers...")
            print(f"   📊 Processing {len(self.all_buy_rules)} rules across {len(worker_args)} ultra-small batches...")
            print(f"   🔥 Using {max_workers} workers (vs {cpu_count} CPU cores) - {max_workers/cpu_count:.1f}x oversubscription!")
            print(f"   💾 Memory optimized: Essential columns only")
            print(f"   ⚡ Update frequency: 0.5s for real-time monitoring")

            # Create shared progress tracking
            with Manager() as manager:
                progress_dict = manager.dict()
                progress_dict['completed_rules'] = 0
                progress_dict['total_rules'] = len(self.all_buy_rules)
                progress_dict['completed_batches'] = 0
                progress_dict['total_batches'] = len(worker_args)
                progress_dict['start_time'] = time.time()

                # Add progress tracking to worker data
                enhanced_worker_args = []
                for i, (batch, data) in enumerate(worker_args):
                    enhanced_data = data.copy()
                    enhanced_data['batch_id'] = i
                    enhanced_data['progress_dict'] = progress_dict
                    enhanced_worker_args.append((batch, enhanced_data))

                # Start progress monitoring
                import threading
                progress_thread = threading.Thread(
                    target=self._monitor_progress_realtime,
                    args=(progress_dict,),
                    daemon=True
                )
                progress_thread.start()

                start_time = time.time()
                # EXTREME OPTIMIZATION: Use all available workers without limit
                actual_workers = min(len(worker_args), max_workers)
                print(f"   🔥 Launching {actual_workers} workers for {len(worker_args)} batches...")

                with Pool(processes=actual_workers, maxtasksperchild=1) as pool:
                    # Use maxtasksperchild=1 to prevent memory buildup and ensure fresh workers
                    batch_results = pool.map(process_rule_batch_worker_with_progress, enhanced_worker_args)
                end_time = time.time()

                # Final progress update
                progress_dict['completed_rules'] = progress_dict['total_rules']
                progress_dict['completed_batches'] = progress_dict['total_batches']

                print(f"\n   ✅ MAXIMUM SPEED ACHIEVED!")
                print(f"   ⏱️ Total time: {end_time - start_time:.1f} seconds")
                print(f"   🚀 Processing rate: {len(self.all_buy_rules) / (end_time - start_time):.1f} rules/second")

            # Combine results from all workers
            all_rule_results = {}
            for batch_result in batch_results:
                if batch_result:
                    all_rule_results.update(batch_result)

            print(f"   ✅ Parallel processing completed! Processed {len(all_rule_results)} rules")
            return all_rule_results

        except Exception as e:
            print(f"   ❌ Multiprocessing failed: {e}")
            print(f"   🔄 Falling back to sequential processing...")
            return self._run_sequential_backtests(start_idx, end_idx)

        # Use multiprocessing with progress tracking
        try:
            from tqdm import tqdm

            # Create a manager for shared progress tracking
            with Manager() as manager:
                progress_dict = manager.dict()
                progress_dict['completed'] = 0
                progress_dict['total'] = len(rule_data)

                # Start progress monitoring in separate process
                progress_process = mp.Process(
                    target=self._monitor_progress,
                    args=(progress_dict,)
                )
                progress_process.start()

                # Run parallel backtests
                with Pool(processes=max_workers) as pool:
                    results = pool.map(self._run_single_rule_backtest_worker, rule_data)

                # Stop progress monitoring
                progress_dict['completed'] = progress_dict['total']
                progress_process.join(timeout=1)
                if progress_process.is_alive():
                    progress_process.terminate()

        except ImportError:
            # Fallback without progress tracking
            with Pool(processes=max_workers) as pool:
                results = pool.map(self._run_single_rule_backtest_worker, rule_data)

        # Convert results to dictionary
        all_rule_results = {}
        for result in results:
            if result:
                all_rule_results[result['rule_name']] = result

        return all_rule_results

    def _monitor_progress_realtime(self, progress_dict):
        """Real-time progress monitoring for maximum speed optimization"""
        import time

        start_time = progress_dict.get('start_time', time.time())
        total_rules = progress_dict.get('total_rules', 1)
        total_batches = progress_dict.get('total_batches', 1)

        print(f"   📊 Real-time progress monitoring started...")

        while True:
            try:
                completed_rules = progress_dict.get('completed_rules', 0)
                completed_batches = progress_dict.get('completed_batches', 0)

                if completed_rules >= total_rules:
                    break

                elapsed = time.time() - start_time
                if elapsed > 0:
                    rules_per_sec = completed_rules / elapsed
                    eta_seconds = (total_rules - completed_rules) / rules_per_sec if rules_per_sec > 0 else 0

                    progress_pct = (completed_rules / total_rules) * 100
                    batch_progress_pct = (completed_batches / total_batches) * 100

                    print(f"\r   🚀 Progress: {completed_rules}/{total_rules} rules ({progress_pct:.1f}%) | "
                          f"Batches: {completed_batches}/{total_batches} ({batch_progress_pct:.1f}%) | "
                          f"Speed: {rules_per_sec:.1f} rules/sec | "
                          f"ETA: {eta_seconds:.0f}s", end="", flush=True)

                time.sleep(0.5)  # Update twice per second for extreme monitoring

            except Exception as e:
                # Ignore progress monitoring errors
                time.sleep(1)
                continue

        print()  # New line after progress completion

    def _cache_all_indicators(self, start_idx, end_idx):
        """Pre-calculate and cache ALL technical indicators for maximum speed"""

        # Get the data slice we'll be working with
        df_slice = self.df.iloc[start_idx:end_idx].copy()

        print(f"   📈 Caching technical indicators for {len(df_slice)} candles...")

        # Cache all the indicators that rules commonly use
        # This prevents recalculation for every rule

        # Basic price data (already available)
        self._cached_close = df_slice['close'].values
        self._cached_high = df_slice['high'].values
        self._cached_low = df_slice['low'].values
        self._cached_open = df_slice['open'].values
        self._cached_volume = df_slice['volume'].values

        # Moving averages (most commonly used)
        self._cached_ma7 = df_slice['close'].rolling(window=7, center=False).mean().values
        self._cached_ma25 = df_slice['close'].rolling(window=25, center=False).mean().values
        self._cached_ma50 = df_slice['close'].rolling(window=50, center=False).mean().values
        self._cached_ma200 = df_slice['close'].rolling(window=200, center=False).mean().values

        # RSI (very commonly used)
        delta = df_slice['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14, center=False).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14, center=False).mean()
        rs = gain / loss
        self._cached_rsi14 = (100 - (100 / (1 + rs))).values

        # Bollinger Bands
        bb_period = 20
        bb_std = 2
        bb_ma = df_slice['close'].rolling(window=bb_period, center=False).mean()
        bb_std_dev = df_slice['close'].rolling(window=bb_period, center=False).std()
        self._cached_bb_upper = (bb_ma + (bb_std_dev * bb_std)).values
        self._cached_bb_lower = (bb_ma - (bb_std_dev * bb_std)).values
        self._cached_bb_middle = bb_ma.values

        # MACD
        ema12 = df_slice['close'].ewm(span=12, adjust=False).mean()
        ema26 = df_slice['close'].ewm(span=26, adjust=False).mean()
        self._cached_macd = (ema12 - ema26).values
        self._cached_macd_signal = self._cached_macd.copy()  # Simplified for speed

        # Volume indicators
        self._cached_volume_ma = df_slice['volume'].rolling(window=20, center=False).mean().values

        # Stochastic
        low_14 = df_slice['low'].rolling(window=14, center=False).min()
        high_14 = df_slice['high'].rolling(window=14, center=False).max()
        self._cached_stoch_k = ((df_slice['close'] - low_14) / (high_14 - low_14) * 100).values

        # Price changes
        self._cached_price_change = df_slice['close'].pct_change().values
        self._cached_price_change_5 = df_slice['close'].pct_change(periods=5).values

        # Store the start index for offset calculations
        self._cache_start_idx = start_idx

        # Set cache reference in indicators for ultra-fast access
        if hasattr(self, 'indicators'):
            self.indicators.set_cache_engine(self, start_idx)

        print(f"   ✅ Cached {len([k for k in self.__dict__.keys() if k.startswith('_cached_')])} indicators successfully")

    def _run_vectorized_backtests(self, rules_to_test, start_idx, end_idx):
        """Run backtests using vectorized signal pre-calculation with INDICATOR CACHING"""
        import time

        print(f"   🚀 INDICATOR CACHING: Pre-calculating ALL technical indicators...")
        cache_start = time.time()

        # Pre-calculate and cache ALL technical indicators for the entire range
        self._cache_all_indicators(start_idx, end_idx)

        cache_time = time.time() - cache_start
        print(f"   ✅ Indicator caching completed in {cache_time:.1f}s")

        print(f"   📊 Pre-calculating signals for {len(rules_to_test)} rules...")
        start_time = time.time()

        # Pre-calculate all signals for all rules using cached indicators
        all_signals = {}

        try:
            from tqdm import tqdm
            rule_iterator = tqdm(rules_to_test, desc="Pre-calculating signals")
        except ImportError:
            rule_iterator = rules_to_test

        for rule_name, rule_func in rule_iterator:
            try:
                # Pre-calculate signals for entire range using cached indicators
                signals = []
                for idx in range(start_idx, end_idx):
                    try:
                        # Apply global market filters before checking rule
                        if self._check_global_market_filters(idx) and rule_func(idx):
                            signals.append(True)
                        else:
                            signals.append(False)
                    except:
                        signals.append(False)
                all_signals[rule_name] = signals
            except Exception as e:
                print(f"   ⚠️ Error pre-calculating signals for {rule_name}: {e}")
                all_signals[rule_name] = [False] * (end_idx - start_idx)

        signal_time = time.time() - start_time
        total_time = cache_time + signal_time
        print(f"   ✅ Signal pre-calculation completed in {signal_time:.1f}s (Total with caching: {total_time:.1f}s)")

        # Now run ultra-fast backtests using pre-calculated signals
        print(f"   🚀 Running ultra-fast backtests...")
        backtest_start = time.time()

        all_rule_results = {}

        try:
            from tqdm import tqdm
            rule_iterator = tqdm(rules_to_test, desc="Running backtests")
        except ImportError:
            rule_iterator = rules_to_test

        for rule_name, rule_func in rule_iterator:
            signals = all_signals[rule_name]
            result = self._run_vectorized_rule_backtest(rule_name, signals, start_idx, end_idx)
            all_rule_results[rule_name] = result

        backtest_time = time.time() - backtest_start
        total_time = time.time() - start_time
        print(f"   ✅ Backtests completed in {backtest_time:.1f}s (Total: {total_time:.1f}s)")

        return all_rule_results

    def _run_vectorized_rule_backtest(self, rule_name, signals, start_idx, end_idx):
        """Run backtest for a single rule using pre-calculated signals - ULTRA FAST"""

        # Initialize isolated trading state
        capital = self.config.INITIAL_CAPITAL
        position = None
        trades = []
        signal_count = 0
        position_active = False

        # Pre-extract price data for maximum speed
        close_prices = self.df['close'].iloc[start_idx:end_idx].values

        # Cache configuration values
        stop_loss_pct = self.current_stop_loss_pct
        take_profit_pct = self.current_take_profit_pct
        position_size_pct = self.config.POSITION_SIZE_PCT

        # Ultra-fast backtest loop using pre-calculated signals
        for i, signal in enumerate(signals):
            current_price = close_prices[i]
            actual_idx = start_idx + i

            # Check for exit conditions if in position
            if position is not None:
                entry_price = position['entry_price']
                current_return = (current_price / entry_price - 1) * 100

                # Check stop loss or take profit
                if current_return <= -stop_loss_pct or current_return >= take_profit_pct:
                    # Execute exit
                    exit_value = position['quantity'] * current_price
                    pnl = exit_value - position['position_value']
                    capital += pnl

                    exit_reason = f"Stop Loss ({current_return:.2f}%)" if current_return <= -stop_loss_pct else f"Take Profit ({current_return:.2f}%)"

                    trades.append({
                        'entry_time': position['entry_time'],
                        'exit_time': f"Index_{actual_idx}",
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'quantity': position['quantity'],
                        'position_value': position['position_value'],
                        'pnl': pnl,
                        'pnl_pct': current_return,
                        'rule_triggered': rule_name,
                        'exit_reason': exit_reason,
                        'entry_idx': position['entry_idx'],
                        'exit_idx': actual_idx,
                        'duration_minutes': actual_idx - position['entry_idx']
                    })
                    position = None
                    position_active = False
                    continue

            # Check for buy signal if not in position
            if not position_active and signal:
                signal_count += 1

                # Enter position
                position_value = capital * position_size_pct
                quantity = position_value / current_price

                position = {
                    'entry_time': f"Index_{actual_idx}",
                    'entry_price': current_price,
                    'quantity': quantity,
                    'position_value': position_value,
                    'entry_idx': actual_idx
                }
                position_active = True

        # Close any remaining position at end
        if position is not None:
            current_price = close_prices[-1]
            exit_value = position['quantity'] * current_price
            pnl = exit_value - position['position_value']
            capital += pnl
            current_return = (current_price / position['entry_price'] - 1) * 100

            trades.append({
                'entry_time': position['entry_time'],
                'exit_time': f"Index_{end_idx - 1}",
                'entry_price': position['entry_price'],
                'exit_price': current_price,
                'quantity': position['quantity'],
                'position_value': position['position_value'],
                'pnl': pnl,
                'pnl_pct': current_return,
                'rule_triggered': rule_name,
                'exit_reason': "End of backtest",
                'entry_idx': position['entry_idx'],
                'exit_idx': end_idx - 1,
                'duration_minutes': (end_idx - 1) - position['entry_idx']
            })

        # Calculate performance metrics
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['pnl'] > 0])
        losing_trades = len([t for t in trades if t['pnl'] < 0])
        neutral_trades = len([t for t in trades if t['pnl'] == 0])

        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        total_return_pct = (capital / self.config.INITIAL_CAPITAL - 1) * 100

        total_profits = sum([t['pnl'] for t in trades if t['pnl'] > 0])
        total_losses = abs(sum([t['pnl'] for t in trades if t['pnl'] < 0]))
        profit_factor = (total_profits / total_losses) if total_losses > 0 else float('inf')

        avg_trade_duration = sum([t['duration_minutes'] for t in trades]) / total_trades if total_trades > 0 else 0

        return {
            'rule_name': rule_name,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'neutral_trades': neutral_trades,
            'win_rate': win_rate,
            'total_return_pct': total_return_pct,
            'profit_factor': profit_factor,
            'total_profits': total_profits,
            'total_losses': total_losses,
            'final_capital': capital,
            'total_signals': signal_count,
            'signal_to_trade_ratio': total_trades / signal_count if signal_count > 0 else 0,
            'avg_trade_duration_minutes': avg_trade_duration,
            'trades': trades
        }

    def _run_sequential_backtests_subset(self, rules_to_test, start_idx, end_idx):
        """Run backtests sequentially for a subset of rules"""
        try:
            from tqdm import tqdm
            use_tqdm = True
        except ImportError:
            print("⚠️ tqdm not available, using basic progress tracking")
            use_tqdm = False

        # Results storage for all rules
        all_rule_results = {}

        # Test each buy rule independently
        rule_iterator = tqdm(rules_to_test, desc="Testing buy rules") if use_tqdm else rules_to_test

        for rule_name, rule_func in rule_iterator:
            if not use_tqdm:
                print(f"   Testing {rule_name}...")

            # Run isolated backtest for this rule
            rule_result = self._run_isolated_rule_backtest(
                rule_name, rule_func, start_idx, end_idx
            )

            all_rule_results[rule_name] = rule_result

        return all_rule_results

    def _run_sequential_backtests(self, start_idx, end_idx):
        """Run backtests sequentially (fallback method)"""
        try:
            from tqdm import tqdm
            use_tqdm = True
        except ImportError:
            print("⚠️ tqdm not available, using basic progress tracking")
            use_tqdm = False

        # Results storage for all rules
        all_rule_results = {}

        # Test each buy rule independently
        rule_iterator = tqdm(self.all_buy_rules, desc="Testing buy rules") if use_tqdm else self.all_buy_rules

        for rule_name, rule_func in rule_iterator:
            if not use_tqdm:
                print(f"   Testing {rule_name}...")

            # Run isolated backtest for this rule
            rule_result = self._run_isolated_rule_backtest(
                rule_name, rule_func, start_idx, end_idx
            )

            all_rule_results[rule_name] = rule_result

        return all_rule_results

    @staticmethod
    def _monitor_progress(progress_dict):
        """Monitor progress across all worker processes"""
        try:
            from tqdm import tqdm
            import time

            pbar = tqdm(total=progress_dict['total'], desc="Testing buy rules")
            last_completed = 0

            while progress_dict['completed'] < progress_dict['total']:
                current_completed = progress_dict['completed']
                if current_completed > last_completed:
                    pbar.update(current_completed - last_completed)
                    last_completed = current_completed
                time.sleep(0.1)

            pbar.update(progress_dict['total'] - last_completed)
            pbar.close()

        except ImportError:
            pass

    @staticmethod
    def _run_single_rule_backtest_worker(rule_data):
        """Worker function for parallel rule backtesting"""
        try:
            rule_name = rule_data['rule_name']
            rule_func = rule_data['rule_func']
            start_idx = rule_data['start_idx']
            end_idx = rule_data['end_idx']
            df = rule_data['df_data']
            config = rule_data['config_data']

            # Reset indices for the data slice
            df = df.reset_index(drop=True)

            # Initialize isolated trading state
            capital = config['INITIAL_CAPITAL']
            position = None
            trades = []
            signals = 0
            position_active = False

            # Main backtest loop for this rule
            for idx in range(len(df)):
                try:
                    # Check for exit conditions if in position
                    if position is not None:
                        current_price = df['close'].iloc[idx]
                        entry_price = position['entry_price']
                        current_return = (current_price / entry_price - 1) * 100

                        # Check stop loss
                        if current_return <= -config['stop_loss_pct']:
                            # Execute stop loss
                            exit_value = position['quantity'] * current_price
                            pnl = exit_value - position['position_value']
                            capital += pnl

                            trade = {
                                'entry_time': position['entry_time'],
                                'exit_time': f"Index_{idx}",
                                'entry_price': position['entry_price'],
                                'exit_price': current_price,
                                'quantity': position['quantity'],
                                'position_value': position['position_value'],
                                'pnl': pnl,
                                'pnl_pct': current_return,
                                'rule_triggered': rule_name,
                                'exit_reason': f"Stop Loss ({current_return:.2f}%)",
                                'entry_idx': position['entry_idx'],
                                'exit_idx': idx,
                                'duration_minutes': idx - position['entry_idx']
                            }
                            trades.append(trade)
                            position = None
                            position_active = False
                            continue

                        # Check take profit
                        if current_return >= config['take_profit_pct']:
                            # Execute take profit
                            exit_value = position['quantity'] * current_price
                            pnl = exit_value - position['position_value']
                            capital += pnl

                            trade = {
                                'entry_time': position['entry_time'],
                                'exit_time': f"Index_{idx}",
                                'entry_price': position['entry_price'],
                                'exit_price': current_price,
                                'quantity': position['quantity'],
                                'position_value': position['position_value'],
                                'pnl': pnl,
                                'pnl_pct': current_return,
                                'rule_triggered': rule_name,
                                'exit_reason': f"Take Profit ({current_return:.2f}%)",
                                'entry_idx': position['entry_idx'],
                                'exit_idx': idx,
                                'duration_minutes': idx - position['entry_idx']
                            }
                            trades.append(trade)
                            position = None
                            position_active = False
                            continue

                    # Check for buy signal if not in position
                    if not position_active:
                        # Apply global market filters before checking rule
                        if check_global_market_filters_worker(df, idx, config) and rule_func(start_idx + idx):
                            signals += 1

                            # Enter position
                            price = df['close'].iloc[idx]
                            position_value = capital * config['POSITION_SIZE_PCT']
                            quantity = position_value / price

                            position = {
                                'entry_time': f"Index_{idx}",
                                'entry_price': price,
                                'quantity': quantity,
                                'position_value': position_value,
                                'entry_idx': idx
                            }
                            position_active = True

                except Exception as e:
                    # Skip if rule fails
                    continue

            # Close any remaining position at end
            if position is not None:
                current_price = df['close'].iloc[-1]
                exit_value = position['quantity'] * current_price
                pnl = exit_value - position['position_value']
                capital += pnl
                current_return = (current_price / position['entry_price'] - 1) * 100

                trade = {
                    'entry_time': position['entry_time'],
                    'exit_time': f"Index_{len(df) - 1}",
                    'entry_price': position['entry_price'],
                    'exit_price': current_price,
                    'quantity': position['quantity'],
                    'position_value': position['position_value'],
                    'pnl': pnl,
                    'pnl_pct': current_return,
                    'rule_triggered': rule_name,
                    'exit_reason': "End of backtest",
                    'entry_idx': position['entry_idx'],
                    'exit_idx': len(df) - 1,
                    'duration_minutes': (len(df) - 1) - position['entry_idx']
                }
                trades.append(trade)

            # Calculate performance metrics
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t['pnl'] > 0])
            losing_trades = len([t for t in trades if t['pnl'] < 0])
            neutral_trades = len([t for t in trades if t['pnl'] == 0])

            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            total_return_pct = (capital / config['INITIAL_CAPITAL'] - 1) * 100

            total_profits = sum([t['pnl'] for t in trades if t['pnl'] > 0])
            total_losses = abs(sum([t['pnl'] for t in trades if t['pnl'] < 0]))
            profit_factor = (total_profits / total_losses) if total_losses > 0 else float('inf')

            avg_trade_duration = sum([t['duration_minutes'] for t in trades]) / total_trades if total_trades > 0 else 0

            return {
                'rule_name': rule_name,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'neutral_trades': neutral_trades,
                'win_rate': win_rate,
                'total_return_pct': total_return_pct,
                'profit_factor': profit_factor,
                'total_profits': total_profits,
                'total_losses': total_losses,
                'final_capital': capital,
                'total_signals': signals,
                'signal_to_trade_ratio': total_trades / signals if signals > 0 else 0,
                'avg_trade_duration_minutes': avg_trade_duration,
                'trades': trades
            }

        except Exception as e:
            print(f"Error processing rule {rule_data.get('rule_name', 'Unknown')}: {e}")
            return None

    def _run_independent_backtests(self, start_idx, end_idx, stop_loss_pct=None, take_profit_pct=None):
        """Run independent backtests where each buy rule operates in isolation - ULTRA OPTIMIZED"""

        # Use provided SL/TP or defaults
        self.current_stop_loss_pct = stop_loss_pct or self.config.STOP_LOSS_PCT
        self.current_take_profit_pct = take_profit_pct or self.config.TAKE_PROFIT_PCT

        print(f"🎯 Running INDEPENDENT backtests...")
        print(f"   SL: {self.current_stop_loss_pct}% | TP: {self.current_take_profit_pct}%")

        # Check for ultra-fast mode
        if getattr(self.config, 'ULTRA_FAST_MODE', False):
            sample_rules = getattr(self.config, 'SAMPLE_RULES_ONLY', 10)
            rules_to_test = self.all_buy_rules[:sample_rules]
            print(f"   🚀 ULTRA-FAST MODE: Testing only {len(rules_to_test)} rules for speed")
        else:
            rules_to_test = self.all_buy_rules
            print(f"   Testing {len(rules_to_test)} buy rules independently")

        # Check if we should use vectorized approach
        if getattr(self.config, 'USE_VECTORIZED_SIGNALS', True):
            print(f"   ⚡ Using vectorized signal pre-calculation...")
            all_rule_results = self._run_vectorized_backtests(rules_to_test, start_idx, end_idx)
        else:
            # Check if multiprocessing should be used
            use_multiprocessing = getattr(self.config, 'USE_MULTIPROCESSING', True)

            if use_multiprocessing and len(rules_to_test) > 4:
                print(f"🚀 Using multiprocessing for faster execution...")
                all_rule_results = self._run_parallel_backtests(start_idx, end_idx)
            else:
                print(f"🔄 Using sequential processing...")
                all_rule_results = self._run_sequential_backtests_subset(rules_to_test, start_idx, end_idx)

        # Calculate aggregated statistics
        # Only include rules that actually made trades
        rules_with_trades = {k: v for k, v in all_rule_results.items() if v['total_trades'] > 0}

        total_trades = sum(result['total_trades'] for result in rules_with_trades.values())

        # For independent evaluation, calculate total return as sum of positive returns only
        # This represents the combined performance of profitable rules
        positive_returns = [result['total_return_pct'] for result in rules_with_trades.values() if result['total_return_pct'] > 0]
        total_return = sum(positive_returns) if positive_returns else 0

        # Calculate overall win rate (weighted by number of trades)
        total_wins = sum(result['winning_trades'] for result in rules_with_trades.values())
        overall_win_rate = (total_wins / total_trades * 100) if total_trades > 0 else 0

        # Calculate overall profit factor
        total_profits = sum(result['total_profits'] for result in rules_with_trades.values())
        total_losses = sum(result['total_losses'] for result in rules_with_trades.values())
        overall_profit_factor = (total_profits / total_losses) if total_losses > 0 else float('inf')

        print(f"\n✅ INDEPENDENT BACKTESTS COMPLETED")
        print(f"   Total Rules Tested: {len(all_rule_results)}")
        print(f"   Total Trades: {total_trades:,}")
        print(f"   Combined Return: {total_return:.2f}%")
        print(f"   Overall Win Rate: {overall_win_rate:.1f}%")
        print(f"   Overall Profit Factor: {overall_profit_factor:.2f}")

        return {
            'evaluation_method': 'INDEPENDENT',
            'rule_results': all_rule_results,
            'total_trades': total_trades,
            'total_return_pct': total_return,
            'overall_win_rate': overall_win_rate,
            'overall_profit_factor': overall_profit_factor,
            'backtest_info': {
                'start_idx': start_idx,
                'end_idx': end_idx,
                'total_candles': end_idx - start_idx,
                'stop_loss_pct': self.current_stop_loss_pct,
                'take_profit_pct': self.current_take_profit_pct,
                'rules_tested': len(all_rule_results)
            }
        }

    def _run_isolated_rule_backtest(self, rule_name, rule_func, start_idx, end_idx):
        """Run backtest for a single rule in complete isolation - OPTIMIZED"""

        # Initialize isolated trading state
        capital = self.config.INITIAL_CAPITAL
        position = None
        trades = []
        signals = 0
        position_active = False

        # Pre-extract price data for faster access
        close_prices = self.df['close'].iloc[start_idx:end_idx].values

        # Cache configuration values
        stop_loss_pct = self.current_stop_loss_pct
        take_profit_pct = self.current_take_profit_pct
        position_size_pct = self.config.POSITION_SIZE_PCT

        # Main backtest loop for this rule - OPTIMIZED
        for i, idx in enumerate(range(start_idx, end_idx)):
            try:
                current_price = close_prices[i]

                # Check for exit conditions if in position
                if position is not None:
                    entry_price = position['entry_price']
                    current_return = (current_price / entry_price - 1) * 100

                    # Check stop loss or take profit
                    if current_return <= -stop_loss_pct:
                        # Execute stop loss
                        exit_value = position['quantity'] * current_price
                        pnl = exit_value - position['position_value']
                        capital += pnl

                        trades.append({
                            'entry_time': position['entry_time'],
                            'exit_time': f"Index_{idx}",
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'quantity': position['quantity'],
                            'position_value': position['position_value'],
                            'pnl': pnl,
                            'pnl_pct': current_return,
                            'rule_triggered': rule_name,
                            'exit_reason': f"Stop Loss ({current_return:.2f}%)",
                            'entry_idx': position['entry_idx'],
                            'exit_idx': idx,
                            'duration_minutes': idx - position['entry_idx']
                        })
                        position = None
                        position_active = False
                        continue

                    elif current_return >= take_profit_pct:
                        # Execute take profit
                        exit_value = position['quantity'] * current_price
                        pnl = exit_value - position['position_value']
                        capital += pnl

                        trades.append({
                            'entry_time': position['entry_time'],
                            'exit_time': f"Index_{idx}",
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'quantity': position['quantity'],
                            'position_value': position['position_value'],
                            'pnl': pnl,
                            'pnl_pct': current_return,
                            'rule_triggered': rule_name,
                            'exit_reason': f"Take Profit ({current_return:.2f}%)",
                            'entry_idx': position['entry_idx'],
                            'exit_idx': idx,
                            'duration_minutes': idx - position['entry_idx']
                        })
                        position = None
                        position_active = False
                        continue

                # Check for buy signal if not in position
                if not position_active:
                    signals += 1
                    # Apply global market filters before checking rule
                    if self._check_global_market_filters(idx) and rule_func(idx):
                        # Enter position
                        position_value = capital * position_size_pct
                        quantity = position_value / current_price

                        position = {
                            'entry_time': f"Index_{idx}",
                            'entry_price': current_price,
                            'quantity': quantity,
                            'position_value': position_value,
                            'entry_idx': idx
                        }
                        position_active = True

            except Exception:
                # Skip if rule fails - simplified error handling
                continue

        # Close any remaining position at end
        if position is not None:
            current_price = self.df['close'].iloc[end_idx - 1]
            exit_value = position['quantity'] * current_price
            pnl = exit_value - position['position_value']
            capital += pnl
            current_return = (current_price / position['entry_price'] - 1) * 100

            trade = {
                'entry_time': position['entry_time'],
                'exit_time': f"Index_{end_idx - 1}",
                'entry_price': position['entry_price'],
                'exit_price': current_price,
                'quantity': position['quantity'],
                'position_value': position['position_value'],
                'pnl': pnl,
                'pnl_pct': current_return,
                'rule_triggered': rule_name,
                'exit_reason': "End of backtest",
                'entry_idx': position['entry_idx'],
                'exit_idx': end_idx - 1,
                'duration_minutes': (end_idx - 1) - position['entry_idx']
            }
            trades.append(trade)

        # Calculate performance metrics
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['pnl'] > 0])
        losing_trades = len([t for t in trades if t['pnl'] < 0])
        neutral_trades = len([t for t in trades if t['pnl'] == 0])

        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        total_return_pct = (capital / self.config.INITIAL_CAPITAL - 1) * 100

        total_profits = sum([t['pnl'] for t in trades if t['pnl'] > 0])
        total_losses = abs(sum([t['pnl'] for t in trades if t['pnl'] < 0]))
        profit_factor = (total_profits / total_losses) if total_losses > 0 else float('inf')

        avg_trade_duration = sum([t['duration_minutes'] for t in trades]) / total_trades if total_trades > 0 else 0

        return {
            'rule_name': rule_name,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'neutral_trades': neutral_trades,
            'win_rate': win_rate,
            'total_return_pct': total_return_pct,
            'profit_factor': profit_factor,
            'total_profits': total_profits,
            'total_losses': total_losses,
            'final_capital': capital,
            'total_signals': signals,
            'signal_to_trade_ratio': total_trades / signals if signals > 0 else 0,
            'avg_trade_duration_minutes': avg_trade_duration,
            'trades': trades
        }

    def _run_unified_backtest(self, start_idx, end_idx, stop_loss_pct=None, take_profit_pct=None):
        """Run unified backtest where all rules are evaluated simultaneously with dynamic SL/TP"""

        # Use provided SL/TP or defaults
        self.current_stop_loss_pct = stop_loss_pct or self.config.STOP_LOSS_PCT
        self.current_take_profit_pct = take_profit_pct or self.config.TAKE_PROFIT_PCT

        # Reset trading state
        self.current_capital = self.initial_capital
        self.position = None  # Keep for backward compatibility
        self.positions = []   # NEW: List to track multiple concurrent positions
        self.max_concurrent_trades = getattr(self.config, 'MAX_CONCURRENT_TRADES', 1)

        # Results storage
        all_trades = []
        rule_performance = {rule_name: {
            'signals': 0, 'entries': 0, 'exits': 0, 'total_pnl': 0, 'total_pnl_pct': 0,
            'wins': 0, 'losses': 0, 'total_win_pnl': 0, 'total_loss_pnl': 0, 'trades': []
        } for rule_name, _ in self.all_buy_rules}
        sell_rule_performance = {rule_name: {'signals': 0, 'exits': 0, 'successful_exits': 0, 'post_exit_analysis': []}
                                for rule_name, _ in self.all_sell_rules}

        print(f"Starting unified backtest...")

        # Main backtest loop
        for idx in range(start_idx, end_idx):

            # Print progress
            if idx % self.config.PROGRESS_INTERVAL == 0:
                progress = (idx - start_idx) / (end_idx - start_idx) * 100
                print(f"  Progress: {progress:.1f}% - Index {idx:,} - Capital: ${self.current_capital:,.0f}")

            # Check exit conditions for all open positions
            if self.max_concurrent_trades > 1:
                # Multiple positions mode
                self._check_multiple_positions_exit_conditions(idx, all_trades, sell_rule_performance, rule_performance)
            elif self.position is not None:
                # Single position mode (backward compatibility)
                exit_result = self._check_unified_exit_conditions(idx, all_trades, sell_rule_performance)
                if exit_result:
                    continue  # Position was closed

            # Check for buy signals
            if self.max_concurrent_trades > 1:
                # Multiple positions mode - check if we can open more positions
                if len(self.positions) < self.max_concurrent_trades:
                    # Check if sell rule blocking is enabled
                    if self.config.BLOCK_BUY_IF_SELL_ACTIVE:
                        # Check for sell conditions before allowing buy signals
                        active_sell_rules = self._evaluate_all_sell_rules(idx, sell_rule_performance)

                        # Only proceed with buy signals if no sell conditions are active
                        if not active_sell_rules:
                            active_buy_rules = self._evaluate_all_buy_rules_for_multiple_positions(idx, rule_performance)

                            if active_buy_rules:
                                # Enter trade with all active rules recorded
                                self._execute_multiple_positions_buy(idx, active_buy_rules, rule_performance)
                        else:
                            # Log blocked buy signal due to active sell conditions
                            if idx % 10000 == 0:  # Reduce log frequency
                                print(f"    🚫 Buy signals blocked at idx {idx} due to active sell conditions: {', '.join(active_sell_rules[:3])}")
                    else:
                        # Standard behavior: allow buy signals regardless of sell conditions
                        active_buy_rules = self._evaluate_all_buy_rules_for_multiple_positions(idx, rule_performance)

                        if active_buy_rules:
                            # Enter trade with all active rules recorded
                            self._execute_multiple_positions_buy(idx, active_buy_rules, rule_performance)
            else:
                # Single position mode (backward compatibility)
                if self.position is None:
                    # Check if sell rule blocking is enabled
                    if self.config.BLOCK_BUY_IF_SELL_ACTIVE:
                        # Check for sell conditions before allowing buy signals
                        active_sell_rules = self._evaluate_all_sell_rules(idx, sell_rule_performance)

                        # Only proceed with buy signals if no sell conditions are active
                        if not active_sell_rules:
                            active_buy_rules = self._evaluate_all_buy_rules(idx, rule_performance)

                            if active_buy_rules:
                                # Enter trade with all active rules recorded
                                self._execute_unified_buy(idx, active_buy_rules, rule_performance)
                        else:
                            # Log blocked buy signal due to active sell conditions
                            if idx % 10000 == 0:  # Reduce log frequency
                                print(f"    🚫 Buy signals blocked at idx {idx} due to active sell conditions: {', '.join(active_sell_rules[:3])}")
                    else:
                        # Standard behavior: allow buy signals regardless of sell conditions
                        active_buy_rules = self._evaluate_all_buy_rules(idx, rule_performance)

                        if active_buy_rules:
                            # Enter trade with all active rules recorded
                            self._execute_unified_buy(idx, active_buy_rules, rule_performance)

        # Close any remaining positions
        if self.max_concurrent_trades > 1:
            # Close all remaining positions in multiple positions mode
            for position in self.positions[:]:  # Use slice copy to avoid modification during iteration
                self._execute_multiple_positions_sell(end_idx - 1, "End of backtest", [], all_trades, sell_rule_performance, position, rule_performance)
        elif self.position is not None:
            # Close single position (backward compatibility)
            self._execute_unified_sell(end_idx - 1, "End of backtest", [], all_trades, sell_rule_performance)

        # Calculate final performance metrics
        final_performance = self._calculate_unified_performance(all_trades, rule_performance, sell_rule_performance)

        print(f"\nUnified backtest completed:")
        print(f"  Total trades: {len(all_trades)}")
        print(f"  Final capital: ${self.current_capital:,.0f}")
        print(f"  Total return: {((self.current_capital / self.initial_capital) - 1) * 100:.2f}%")

        return {
            'trades': all_trades,
            'buy_rule_performance': rule_performance,
            'sell_rule_performance': sell_rule_performance,
            'final_performance': final_performance,
            'total_trades': len(all_trades),
            'final_capital': self.current_capital,
            'total_return_pct': ((self.current_capital / self.initial_capital) - 1) * 100
        }

    def _check_global_market_filters(self, idx):
        """Check if global market conditions allow buy orders"""
        if not self.config.ENABLE_GLOBAL_MARKET_FILTERS:
            return True

        # Check EMA200 filter - price must be above EMA200
        if self.config.GLOBAL_EMA200_FILTER:
            if 'EMA_200' in self.df.columns:
                current_price = self.df['close'].iloc[idx]
                ema200_value = self.df['EMA_200'].iloc[idx]
                if not pd.isna(ema200_value) and current_price < ema200_value:
                    return False

        # Check RSI overbought filter - RSI must be below threshold
        if self.config.GLOBAL_RSI_OVERBOUGHT_FILTER:
            if 'RSI' in self.df.columns:
                rsi_value = self.df['RSI'].iloc[idx]
                if not pd.isna(rsi_value) and rsi_value > self.config.GLOBAL_RSI_OVERBOUGHT_THRESHOLD:
                    return False

        # Check daily drop filter - block trades when daily drop >= threshold
        if self.config.ENABLE_DAILY_DROP_FILTER:
            daily_drop = self._calculate_daily_drop(idx)
            if daily_drop >= self.config.DAILY_DROP_THRESHOLD:
                return False

        return True

    def _calculate_daily_drop(self, idx):
        """Calculate the daily drop percentage for unified backtest"""
        try:
            if idx < 1:
                return 0.0

            # Get current price
            current_price = self.df['close'].iloc[idx]

            # Find the daily high (look back up to 1440 minutes = 24 hours for 1-minute data)
            lookback_period = min(1440, idx + 1)  # Don't go beyond available data
            start_idx = max(0, idx - lookback_period + 1)

            # Get the highest price in the current day
            daily_high = self.df['high'].iloc[start_idx:idx + 1].max()

            if daily_high <= 0:
                return 0.0

            # Calculate drop percentage from daily high
            daily_drop = ((daily_high - current_price) / daily_high) * 100

            return max(0.0, daily_drop)  # Return 0 if price is above daily high

        except Exception:
            return 0.0  # Return 0 if calculation fails

    def _evaluate_all_buy_rules(self, idx, rule_performance):
        """Evaluate all buy rules simultaneously and return active ones"""

        # First check global market filters
        if not self._check_global_market_filters(idx):
            return []  # Block all buy signals if market conditions are unfavorable

        active_rules = []

        for rule_name, rule_func in self.all_buy_rules:
            try:
                # Count signal
                rule_performance[rule_name]['signals'] += 1

                # Test rule
                if rule_func(idx):
                    active_rules.append(rule_name)

            except Exception as e:
                # Skip if rule fails
                continue

        return active_rules

    def _evaluate_all_sell_rules(self, idx, sell_rule_performance):
        """Evaluate all sell rules simultaneously and return active ones"""

        active_sell_rules = []

        for rule_name, rule_func in self.all_sell_rules:
            try:
                # Test rule first
                if rule_func(idx):
                    # CRITICAL FIX #2: Only count signals when rule actually triggers
                    # This prevents all sell rules from having the same signal count
                    sell_rule_performance[rule_name]['signals'] += 1
                    active_sell_rules.append(rule_name)

            except Exception as e:
                # Skip if rule fails silently - this might be hiding issues
                print(f"⚠️ Sell rule {rule_name} failed at idx {idx}: {str(e)}")
                continue

        return active_sell_rules

    def _check_unified_exit_conditions(self, idx, all_trades, sell_rule_performance):
        """Check all exit conditions in unified system with dynamic SL/TP"""

        if self.position is None:
            return False

        current_price = self.df['close'].iloc[idx]
        entry_price = self.position['entry_price']
        current_return = (current_price / entry_price - 1) * 100
        holding_period = idx - self.position['entry_idx']

        # Update dynamic stop loss if enabled
        if self.config.ENABLE_DYNAMIC_SL:
            self._update_dynamic_stop_loss(current_return, idx, sell_rule_performance)

        # Check rule-based sell signals first (if sell rules are enabled)
        if not self.config.DISABLE_ALL_SELL_RULES:
            active_sell_rules = self._evaluate_all_sell_rules(idx, sell_rule_performance)

            if active_sell_rules:
                # Exit due to sell rule(s)
                exit_reason = f"Sell Rules: {', '.join(active_sell_rules[:3])}"  # Limit display
                self._execute_unified_sell(idx, exit_reason, active_sell_rules, all_trades, sell_rule_performance)
                return True

        # Check dynamic stop loss
        current_sl = self.position.get('current_stop_loss', self.current_stop_loss_pct)
        if current_return <= -current_sl:
            self._execute_unified_sell(idx, f"Dynamic Stop Loss ({current_return:.2f}%)", [], all_trades, sell_rule_performance)
            return True

        # Check take profit with trailing logic
        if current_return >= self.current_take_profit_pct:
            # Check if we should trail or exit
            if self._should_trail_stop_loss(idx):
                # Activate trailing mode and set initial trailing SL
                if not self.position.get('is_trailing', False):
                    self._activate_trailing_stop_loss(current_return)
                else:
                    # Already in trailing mode, update if needed
                    self._update_trailing_stop_loss(current_return)
                return False  # Don't exit, continue with trailing SL
            else:
                self._execute_unified_sell(idx, f"Take Profit ({current_return:.2f}%)", [], all_trades, sell_rule_performance)
                return True

        # If already in trailing mode, continue to update trailing SL
        elif self.position.get('is_trailing', False):
            self._update_trailing_stop_loss(current_return)

        # Remove time-based exit to allow better trend capture
        if self.config.MAX_HOLDING_PERIOD is not None and holding_period >= self.config.MAX_HOLDING_PERIOD:
            self._execute_unified_sell(idx, f"Max Holding ({holding_period} min)", [], all_trades, sell_rule_performance)
            return True

        return False

    def _update_dynamic_stop_loss(self, current_return, idx, sell_rule_performance):
        """Update stop loss dynamically based on price movement"""

        # Initialize dynamic SL if not set
        if 'current_stop_loss' not in self.position:
            self.position['current_stop_loss'] = self.current_stop_loss_pct
            self.position['highest_return'] = current_return

        # Update highest return achieved
        if current_return > self.position['highest_return']:
            self.position['highest_return'] = current_return

        # Tighten stop loss when price moves favorably (CORRECTED LOGIC)
        if current_return > self.config.DYNAMIC_SL_TRIGGER:
            # Calculate new tighter stop loss (REDUCE the loss percentage)
            new_sl = self.current_stop_loss_pct - self.config.DYNAMIC_SL_TIGHTENING

            # Only tighten (reduce loss), never loosen
            # new_sl should be SMALLER (less negative) than current SL
            if new_sl < self.position['current_stop_loss']:
                self.position['current_stop_loss'] = new_sl

    def _should_trail_stop_loss(self, idx):
        """Determine if we should trail stop loss instead of taking profit"""

        if not self.config.TRAIL_ON_TP_HIT:
            return False

        # Check if buy signals are still active
        active_buy_rules = self._evaluate_all_buy_rules(idx, {})

        # Check if no sell signals are active (only if sell rules are enabled)
        if self.config.DISABLE_ALL_SELL_RULES:
            # If sell rules are disabled, only check buy signals for trailing
            return len(active_buy_rules) > 0
        else:
            active_sell_rules = self._evaluate_all_sell_rules(idx, {})
            # Trail if buy signals still active and no sell signals
            return len(active_buy_rules) > 0 and len(active_sell_rules) == 0

    def _activate_trailing_stop_loss(self, current_return):
        """Activate trailing stop loss mode when TP is first hit"""

        # Initialize trailing mode
        self.position['is_trailing'] = True
        self.position['highest_return'] = current_return

        # Set initial trailing SL to current return minus buffer
        initial_trailing_sl = current_return - self.config.TRAIL_BUFFER
        self.position['current_stop_loss'] = initial_trailing_sl

        print(f"    🎯 TRAILING ACTIVATED: Return {current_return:.2f}%, SL set to {initial_trailing_sl:.2f}%")

    def _update_trailing_stop_loss(self, current_return):
        """Continuously update trailing stop loss as profit increases"""

        # Get current highest return
        highest_return = self.position.get('highest_return', current_return)

        # Update highest return if current is higher
        if current_return > highest_return:
            self.position['highest_return'] = current_return

            # Calculate new trailing SL (highest return minus buffer)
            new_trailing_sl = current_return - self.config.TRAIL_BUFFER

            # Only update if new SL is higher (less negative) than current SL
            current_sl = self.position.get('current_stop_loss', self.current_stop_loss_pct)
            if new_trailing_sl > current_sl:
                self.position['current_stop_loss'] = new_trailing_sl
                print(f"    🔄 TRAILING UPDATE: New high {current_return:.2f}%, SL moved to {new_trailing_sl:.2f}%")

    def _execute_unified_buy(self, idx, active_buy_rules, rule_performance):
        """Execute a buy order in unified system"""

        if self.position is not None:
            return  # Already in position

        price = self.df['close'].iloc[idx]

        # Calculate position size
        position_value = self.current_capital * self.config.POSITION_SIZE_PCT
        quantity = position_value / price

        self.position = {
            'entry_time': f"Index_{idx}",
            'entry_price': price,
            'quantity': quantity,
            'position_value': position_value,
            'active_buy_rules': active_buy_rules,
            'entry_idx': idx
        }

        # Record entry for each active rule
        for rule_name in active_buy_rules:
            rule_performance[rule_name]['entries'] += 1

        print(f"    📈 BUY at {price:.2f} - Rules: {', '.join(active_buy_rules[:3])}")

    def _execute_unified_sell(self, idx, exit_reason, active_sell_rules, all_trades, sell_rule_performance):
        """Execute a sell order in unified system"""

        if self.position is None:
            return  # No position to sell

        price = self.df['close'].iloc[idx]

        # Calculate P&L
        entry_value = self.position['position_value']
        exit_value = self.position['quantity'] * price
        pnl = exit_value - entry_value
        pnl_pct = (price / self.position['entry_price'] - 1) * 100

        # Update capital
        self.current_capital += pnl

        # Analyze post-exit market behavior (look ahead 5-10 candles)
        post_exit_analysis = self._analyze_post_exit_behavior(idx, price)

        # Record trade
        trade = {
            'entry_time': self.position['entry_time'],
            'exit_time': f"Index_{idx}",
            'entry_price': self.position['entry_price'],
            'exit_price': price,
            'quantity': self.position['quantity'],
            'position_value': self.position['position_value'],
            'pnl': pnl,
            'pnl_pct': pnl_pct,
            'active_buy_rules': self.position['active_buy_rules'],
            'exit_reason': exit_reason,
            'active_sell_rules': active_sell_rules,
            'entry_idx': self.position['entry_idx'],
            'exit_idx': idx,
            'holding_period_minutes': idx - self.position['entry_idx'],
            'duration_minutes': idx - self.position['entry_idx'],  # Add consistent field name
            'post_exit_analysis': post_exit_analysis
        }

        all_trades.append(trade)

        # Record exit for sell rules
        for rule_name in active_sell_rules:
            sell_rule_performance[rule_name]['exits'] += 1
            sell_rule_performance[rule_name]['post_exit_analysis'].append(post_exit_analysis)

            # SELL RULE ACCURACY LOGIC:
            # A sell signal is considered "correct" if the price declines by at least 0.5%
            # within 5 candles after the exit. This validates that the sell timing was good.
            if post_exit_analysis['max_decline_5_candles'] < -0.5:  # 0.5% decline threshold
                sell_rule_performance[rule_name]['successful_exits'] += 1

        print(f"    📉 SELL at {price:.2f} - {exit_reason} - P&L: {pnl_pct:.2f}%")

        # Clear position
        self.position = None

    def _check_multiple_positions_exit_conditions(self, idx, all_trades, sell_rule_performance, rule_performance):
        """Check exit conditions for all open positions in multiple positions mode"""
        positions_to_close = []

        for position in self.positions:
            current_price = self.df['close'].iloc[idx]
            entry_price = position['entry_price']
            current_return = (current_price / entry_price - 1) * 100

            # Check stop loss
            if current_return <= -self.current_stop_loss_pct:
                positions_to_close.append((position, f"Stop Loss ({current_return:.2f}%)"))
            # Check take profit
            elif current_return >= self.current_take_profit_pct:
                positions_to_close.append((position, f"Take Profit ({current_return:.2f}%)"))

        # Execute exits
        for position, exit_reason in positions_to_close:
            self._execute_multiple_positions_sell(idx, exit_reason, [], all_trades, sell_rule_performance, position, rule_performance)

    def _evaluate_all_buy_rules_for_multiple_positions(self, idx, rule_performance):
        """Evaluate all buy rules for multiple positions mode, excluding rules with existing positions"""
        active_buy_rules = []

        # Use sequential evaluation (multiprocessing handled at higher level)
        for rule_name, rule_func in self.all_buy_rules:
            try:
                # Skip if this rule already has an open position
                if any(rule_name in pos.get('active_buy_rules', []) for pos in self.positions):
                    continue

                # Apply global market filters before checking rule
                if self._check_global_market_filters(idx) and rule_func(idx):
                    active_buy_rules.append(rule_name)
                    rule_performance[rule_name]['signals'] += 1
            except Exception:
                # Skip if rule fails
                continue

        return active_buy_rules



    def _execute_multiple_positions_buy(self, idx, active_buy_rules, rule_performance):
        """Execute a buy order in multiple positions mode"""
        price = self.df['close'].iloc[idx]

        # Calculate position size (divide available capital by max positions for risk management)
        available_capital = self.current_capital / self.max_concurrent_trades
        position_value = available_capital * self.config.POSITION_SIZE_PCT
        quantity = position_value / price

        new_position = {
            'entry_time': f"Index_{idx}",
            'entry_price': price,
            'quantity': quantity,
            'position_value': position_value,
            'active_buy_rules': active_buy_rules,
            'entry_idx': idx
        }

        self.positions.append(new_position)

        # Record entry for each active rule
        for rule_name in active_buy_rules:
            rule_performance[rule_name]['entries'] += 1

        print(f"    📈 BUY #{len(self.positions)} at {price:.2f} - Rules: {', '.join(active_buy_rules[:3])} - Positions: {len(self.positions)}/{self.max_concurrent_trades}")

    def _execute_multiple_positions_sell(self, idx, exit_reason, active_sell_rules, all_trades, sell_rule_performance, position, rule_performance=None):
        """Execute a sell order for a specific position in multiple positions mode"""
        price = self.df['close'].iloc[idx]

        # Calculate P&L
        entry_value = position['position_value']
        exit_value = position['quantity'] * price
        pnl = exit_value - entry_value
        pnl_pct = (price / position['entry_price'] - 1) * 100

        # Update capital
        self.current_capital += pnl

        # Analyze post-exit market behavior (look ahead 5-10 candles)
        post_exit_analysis = self._analyze_post_exit_behavior(idx, price)

        # Record trade
        trade = {
            'entry_time': position['entry_time'],
            'exit_time': f"Index_{idx}",
            'entry_price': position['entry_price'],
            'exit_price': price,
            'quantity': position['quantity'],
            'position_value': position['position_value'],
            'pnl': pnl,
            'pnl_pct': pnl_pct,
            'active_buy_rules': position['active_buy_rules'],
            'active_sell_rules': active_sell_rules,
            'exit_reason': exit_reason,
            'entry_idx': position['entry_idx'],
            'exit_idx': idx,
            'holding_period_minutes': idx - position['entry_idx'],
            'post_exit_analysis': post_exit_analysis
        }

        all_trades.append(trade)

        # Update rule performance for each rule that contributed to this trade
        if rule_performance:
            for rule_name in position['active_buy_rules']:
                rule_performance[rule_name]['exits'] += 1
                rule_performance[rule_name]['total_pnl'] += pnl
                rule_performance[rule_name]['total_pnl_pct'] += pnl_pct

                if pnl > 0:
                    rule_performance[rule_name]['wins'] += 1
                    rule_performance[rule_name]['total_win_pnl'] += pnl
                else:
                    rule_performance[rule_name]['losses'] += 1
                    rule_performance[rule_name]['total_loss_pnl'] += pnl

        # Update sell rule performance
        for rule_name in active_sell_rules:
            sell_rule_performance[rule_name]['exits'] += 1
            sell_rule_performance[rule_name]['total_pnl'] += pnl
            sell_rule_performance[rule_name]['total_pnl_pct'] += pnl_pct

            if pnl > 0:
                sell_rule_performance[rule_name]['wins'] += 1
            else:
                sell_rule_performance[rule_name]['losses'] += 1

        print(f"    📉 SELL #{len(self.positions)} at {price:.2f} - Reason: {exit_reason} - P&L: {pnl_pct:.2f}% - Remaining: {len(self.positions)-1}")

        # Remove position from list
        self.positions.remove(position)

    def _analyze_post_exit_behavior(self, exit_idx, exit_price):
        """Analyze market behavior after exit to evaluate sell rule effectiveness"""

        analysis = {
            'exit_price': exit_price,
            'max_decline_5_candles': 0,
            'max_decline_10_candles': 0,
            'price_5_candles_later': None,
            'price_10_candles_later': None,
            'exit_was_good_timing': False
        }

        try:
            # Look ahead 5 candles
            future_prices_5 = []
            for i in range(1, 6):
                if exit_idx + i < len(self.df):
                    future_prices_5.append(self.df['close'].iloc[exit_idx + i])

            if future_prices_5:
                min_price_5 = min(future_prices_5)
                analysis['max_decline_5_candles'] = (min_price_5 / exit_price - 1) * 100
                analysis['price_5_candles_later'] = future_prices_5[-1]

            # Look ahead 10 candles
            future_prices_10 = []
            for i in range(1, 11):
                if exit_idx + i < len(self.df):
                    future_prices_10.append(self.df['close'].iloc[exit_idx + i])

            if future_prices_10:
                min_price_10 = min(future_prices_10)
                analysis['max_decline_10_candles'] = (min_price_10 / exit_price - 1) * 100
                analysis['price_10_candles_later'] = future_prices_10[-1]

            # Determine if exit timing was good (price declined by at least 0.5% within 10 candles)
            analysis['exit_was_good_timing'] = analysis['max_decline_10_candles'] < -0.5

        except Exception as e:
            # Handle edge cases
            pass

        return analysis

    def _calculate_unified_performance(self, all_trades, rule_performance, sell_rule_performance):
        """Calculate performance metrics for unified system"""

        # Overall system performance
        total_trades = len(all_trades)
        if total_trades == 0:
            return {'error': 'No trades executed'}

        trades_df = pd.DataFrame(all_trades)

        # System-wide metrics
        total_pnl = trades_df['pnl'].sum()
        total_return_pct = (total_pnl / self.initial_capital) * 100
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        win_rate = (winning_trades / total_trades) * 100

        # Calculate system profit factor
        system_winning_pnl = trades_df[trades_df['pnl'] > 0]['pnl'].sum()
        system_losing_pnl = abs(trades_df[trades_df['pnl'] < 0]['pnl'].sum())
        if system_losing_pnl > 0:
            system_profit_factor = system_winning_pnl / system_losing_pnl
        elif system_winning_pnl > 0:
            system_profit_factor = float('inf')  # All winning trades
        else:
            system_profit_factor = 0.0  # All losing trades

        # Calculate individual buy rule performance
        for rule_name in rule_performance:
            rule_trades = []

            # Find trades where this rule was active
            for trade in all_trades:
                if rule_name in trade['active_buy_rules']:
                    rule_trades.append(trade)

            if rule_trades:
                rule_trades_df = pd.DataFrame(rule_trades)
                rule_pnl = rule_trades_df['pnl'].sum()
                rule_winning = len(rule_trades_df[rule_trades_df['pnl'] > 0])
                rule_win_rate = (rule_winning / len(rule_trades)) * 100

                # Calculate profit factor
                winning_pnl = rule_trades_df[rule_trades_df['pnl'] > 0]['pnl'].sum()
                losing_pnl = abs(rule_trades_df[rule_trades_df['pnl'] < 0]['pnl'].sum())
                if losing_pnl > 0:
                    profit_factor = winning_pnl / losing_pnl
                elif winning_pnl > 0:
                    profit_factor = float('inf')  # All winning trades
                else:
                    profit_factor = 0.0  # All losing trades

                # Calculate average gain/loss and duration statistics (0.0% PnL counts as win)
                winning_trades = rule_trades_df[rule_trades_df['pnl'] >= 0]
                losing_trades = rule_trades_df[rule_trades_df['pnl'] < 0]
                avg_gain_pct = winning_trades['pnl_pct'].mean() if len(winning_trades) > 0 else 0
                avg_loss_pct = losing_trades['pnl_pct'].mean() if len(losing_trades) > 0 else 0

                # Duration statistics
                if 'holding_period_minutes' in rule_trades_df.columns and len(rule_trades_df) > 0:
                    durations = rule_trades_df['holding_period_minutes']
                    avg_duration = float(durations.mean())
                    min_duration = float(durations.min())
                    max_duration = float(durations.max())
                else:
                    avg_duration = min_duration = max_duration = 0.0

                # Calculate maximum drawdown for this rule
                rule_trades_df['cumulative_pnl'] = rule_trades_df['pnl'].cumsum()
                rule_trades_df['equity_curve'] = self.initial_capital + rule_trades_df['cumulative_pnl']
                rule_trades_df['running_max'] = rule_trades_df['equity_curve'].expanding().max()
                rule_trades_df['drawdown_pct'] = ((rule_trades_df['equity_curve'] - rule_trades_df['running_max']) / rule_trades_df['running_max']) * 100
                max_drawdown_pct = abs(rule_trades_df['drawdown_pct'].min()) if len(rule_trades_df) > 0 else 0.0

                rule_performance[rule_name].update({
                    'total_trades': len(rule_trades),
                    'total_pnl': rule_pnl,
                    'win_rate': rule_win_rate,
                    'avg_return_pct': rule_trades_df['pnl_pct'].mean(),
                    'contribution_to_total_return': (rule_pnl / self.initial_capital) * 100,
                    'profit_factor': profit_factor,
                    'avg_gain_pct': avg_gain_pct,
                    'avg_loss_pct': avg_loss_pct,
                    'avg_duration_minutes': avg_duration,
                    'min_duration_minutes': min_duration,
                    'max_duration_minutes': max_duration,
                    'max_drawdown_pct': max_drawdown_pct,  # Add maximum drawdown calculation
                    'trades': rule_trades  # Store individual trades for detailed analysis
                })
            else:
                rule_performance[rule_name].update({
                    'total_trades': 0,
                    'total_pnl': 0,
                    'win_rate': 0,
                    'avg_return_pct': 0,
                    'contribution_to_total_return': 0,
                    'profit_factor': 0.0,
                    'avg_gain_pct': 0,
                    'avg_loss_pct': 0,
                    'avg_duration_minutes': 0,
                    'min_duration_minutes': 0,
                    'max_duration_minutes': 0,
                    'trades': []
                })

        # Calculate sell rule effectiveness
        for rule_name in sell_rule_performance:
            rule_data = sell_rule_performance[rule_name]

            if rule_data['exits'] > 0:
                success_rate = (rule_data['successful_exits'] / rule_data['exits']) * 100

                # Average post-exit decline
                post_exit_declines = [analysis['max_decline_10_candles']
                                    for analysis in rule_data['post_exit_analysis']
                                    if analysis['max_decline_10_candles'] is not None]

                avg_post_exit_decline = np.mean(post_exit_declines) if post_exit_declines else 0

                rule_data.update({
                    'success_rate': success_rate,
                    'avg_post_exit_decline': avg_post_exit_decline,
                    'effectiveness_score': success_rate * abs(avg_post_exit_decline)  # Combined metric
                })
            else:
                rule_data.update({
                    'success_rate': 0,
                    'avg_post_exit_decline': 0,
                    'effectiveness_score': 0
                })

        return {
            'system_performance': {
                'total_trades': total_trades,
                'total_return_pct': total_return_pct,
                'win_rate': win_rate,
                'profit_factor': system_profit_factor,
                'total_pnl': total_pnl,
                'final_capital': self.current_capital
            },
            'buy_rule_rankings': self._rank_buy_rules(rule_performance),
            'sell_rule_rankings': self._rank_sell_rules(sell_rule_performance)
        }

    def _rank_buy_rules(self, rule_performance):
        """Rank buy rules by their contribution and effectiveness"""

        rankings = []

        for rule_name, performance in rule_performance.items():
            if performance['total_trades'] > 0:
                # Calculate composite score
                score = (
                    performance['contribution_to_total_return'] * 0.4 +  # 40% weight on return contribution
                    performance['win_rate'] * 0.3 +                     # 30% weight on win rate
                    min(performance['total_trades'], 100) * 0.3         # 30% weight on trade frequency (capped at 100)
                )

                rankings.append({
                    'rule_name': rule_name,  # Ensure rule_name is properly set
                    'score': score,
                    'signals': performance['signals'],
                    'entries': performance['entries'],
                    'total_trades': performance['total_trades'],
                    'win_rate': performance['win_rate'],
                    'contribution_to_total_return': performance['contribution_to_total_return'],
                    'avg_return_pct': performance['avg_return_pct'],
                    'signal_to_entry_ratio': performance['entries'] / performance['signals'] if performance['signals'] > 0 else 0,
                    'profit_factor': performance.get('profit_factor', 0.0),  # Use calculated profit factor
                    'avg_gain_pct': performance.get('avg_gain_pct', 0),
                    'avg_loss_pct': performance.get('avg_loss_pct', 0),
                    'avg_duration_minutes': performance.get('avg_duration_minutes', 0),
                    'min_duration_minutes': performance.get('min_duration_minutes', 0),
                    'max_duration_minutes': performance.get('max_duration_minutes', 0),
                    'max_drawdown_pct': performance.get('max_drawdown_pct', 0.0),  # Include maximum drawdown
                    'trades': performance.get('trades', []),  # Include individual trades
                    'total_return_pct': performance['contribution_to_total_return']  # Add for compatibility
                })

        # Sort by score
        rankings.sort(key=lambda x: x['score'], reverse=True)

        return rankings

    def _rank_sell_rules(self, sell_rule_performance):
        """Rank sell rules by their effectiveness"""

        rankings = []

        for rule_name, performance in sell_rule_performance.items():
            if performance['exits'] > 0:
                rankings.append({
                    'rule_name': rule_name,  # Ensure rule_name is properly set
                    'signals': performance['signals'],
                    'exits': performance['exits'],
                    'successful_exits': performance['successful_exits'],
                    'success_rate': performance['success_rate'],
                    'avg_post_exit_decline': performance['avg_post_exit_decline'],
                    'effectiveness_score': performance['effectiveness_score'],
                    'signal_to_exit_ratio': performance['exits'] / performance['signals'] if performance['signals'] > 0 else 0,
                    'accuracy': performance['success_rate'],  # Add for compatibility
                    'avg_decline_pct': performance['avg_post_exit_decline'],  # Add for compatibility
                    'correct_signals': performance['successful_exits'],  # Add for compatibility
                    'hit_rate': performance['success_rate'] / 100.0  # Add for compatibility
                })

        # Sort by effectiveness score
        rankings.sort(key=lambda x: x['effectiveness_score'], reverse=True)

        return rankings

    def _test_buy_rules_individually(self, start_idx, end_idx):
        """Test each buy rule individually"""
        
        results = {}
        total_rules = len(self.all_buy_rules)
        
        for i, (rule_name, rule_func) in enumerate(self.all_buy_rules, 1):
            print(f"Testing buy rule {i}/{total_rules}: {rule_name}")
            
            try:
                # Reset trading state
                self.current_capital = self.initial_capital
                self.position = None
                trades = []
                signals = []
                
                # Run backtest for this rule
                for idx in range(start_idx, end_idx):
                    
                    # Print progress
                    if idx % self.config.PROGRESS_INTERVAL == 0:
                        progress = (idx - start_idx) / (end_idx - start_idx) * 100
                        print(f"  Progress: {progress:.1f}% - Index {idx:,}")
                    
                    # Check exit conditions if we have a position
                    if self.position is not None:
                        if self._check_exit_conditions(idx, trades):
                            continue  # Position was closed
                    
                    # Check for buy signal if we don't have a position
                    if self.position is None:
                        try:
                            # Apply global market filters before checking rule
                            if self._check_global_market_filters(idx) and rule_func(idx):
                                signal_info = {
                                    'rule_name': rule_name,
                                    'signal_type': 'BUY',
                                    'idx': idx,
                                    'price': self.df['close'].iloc[idx],
                                    'timestamp': f"Index_{idx}"
                                }
                                signals.append(signal_info)

                                # Execute buy
                                self._execute_buy(idx, rule_name)
                        except Exception as e:
                            # Skip if rule fails
                            continue
                
                # Close any remaining position
                if self.position is not None:
                    self._execute_sell(end_idx - 1, "End of backtest", trades)
                
                # Calculate performance metrics
                performance = self._calculate_rule_performance(trades, rule_name)
                
                results[rule_name] = {
                    'trades': trades,
                    'signals': signals,
                    'performance': performance,
                    'total_signals': len(signals),
                    'total_trades': len(trades),
                    'success': True
                }
                
                print(f"  Completed: {len(trades)} trades, {performance['total_return_pct']:.2f}% return")
                
            except Exception as e:
                print(f"  Error testing {rule_name}: {str(e)}")
                results[rule_name] = {
                    'trades': [],
                    'signals': [],
                    'performance': {},
                    'total_signals': 0,
                    'total_trades': 0,
                    'success': False,
                    'error': str(e)
                }
        
        return results
    
    def _test_sell_rules_individually(self, start_idx, end_idx):
        """Test each sell rule individually (simplified implementation)"""
        
        results = {}
        
        # For sell rules, we need a different approach
        # We'll track how often they correctly predict declines
        
        for rule_name, rule_func in self.all_sell_rules:
            print(f"Testing sell rule: {rule_name}")
            
            try:
                signals = []
                correct_predictions = 0
                total_predictions = 0
                
                for idx in range(start_idx, end_idx - 10):  # Leave room for forward looking
                    try:
                        if rule_func(idx):
                            # Record sell signal
                            current_price = self.df['close'].iloc[idx]
                            
                            # Check if price declines in next 5 periods
                            future_prices = [self.df['close'].iloc[i] 
                                           for i in range(idx + 1, min(idx + 6, len(self.df)))]
                            
                            if future_prices:
                                min_future_price = min(future_prices)
                                decline_pct = (min_future_price / current_price - 1) * 100
                                
                                signal_info = {
                                    'rule_name': rule_name,
                                    'signal_type': 'SELL',
                                    'idx': idx,
                                    'price': current_price,
                                    'decline_pct': decline_pct,
                                    'correct': decline_pct < -1.0  # 1% decline threshold
                                }
                                
                                signals.append(signal_info)
                                total_predictions += 1
                                
                                if decline_pct < -1.0:
                                    correct_predictions += 1
                    
                    except Exception as e:
                        continue
                
                # Calculate sell rule performance
                accuracy = (correct_predictions / total_predictions * 100) if total_predictions > 0 else 0
                avg_decline = np.mean([s['decline_pct'] for s in signals if s['correct']]) if signals else 0
                
                results[rule_name] = {
                    'signals': signals,
                    'total_signals': len(signals),
                    'correct_predictions': correct_predictions,
                    'total_predictions': total_predictions,
                    'accuracy': accuracy,
                    'avg_decline_when_correct': avg_decline,
                    'success': True
                }
                
                print(f"  Completed: {total_predictions} signals, {accuracy:.1f}% accuracy")
                
            except Exception as e:
                print(f"  Error testing {rule_name}: {str(e)}")
                results[rule_name] = {
                    'signals': [],
                    'total_signals': 0,
                    'success': False,
                    'error': str(e)
                }
        
        return results
    
    def _check_exit_conditions(self, idx, trades):
        """Check all exit conditions"""
        if self.position is None:
            return False
        
        current_price = self.df['close'].iloc[idx]
        entry_price = self.position['entry_price']
        current_return = (current_price / entry_price - 1) * 100
        holding_period = idx - self.position['entry_idx']
        
        # Stop loss
        if current_return <= -self.config.STOP_LOSS_PCT:
            self._execute_sell(idx, f"Stop Loss ({current_return:.2f}%)", trades)
            return True
        
        # Take profit
        if current_return >= self.config.TAKE_PROFIT_PCT:
            self._execute_sell(idx, f"Take Profit ({current_return:.2f}%)", trades)
            return True
        
        # Max holding period (disabled for better trend capture)
        if self.config.MAX_HOLDING_PERIOD is not None and holding_period >= self.config.MAX_HOLDING_PERIOD:
            self._execute_sell(idx, f"Max Holding ({holding_period} min)", trades)
            return True
        
        return False
    
    def _execute_buy(self, idx, rule_triggered):
        """Execute a buy order"""
        if self.position is not None:
            return  # Already in position
        
        price = self.df['close'].iloc[idx]
        
        # Calculate position size
        position_value = self.current_capital * self.config.POSITION_SIZE_PCT
        quantity = position_value / price
        
        self.position = {
            'entry_time': f"Index_{idx}",
            'entry_price': price,
            'quantity': quantity,
            'position_value': position_value,
            'rule_triggered': rule_triggered,
            'entry_idx': idx
        }
    
    def _execute_sell(self, idx, exit_reason, trades):
        """Execute a sell order"""
        if self.position is None:
            return  # No position to sell
        
        price = self.df['close'].iloc[idx]
        
        # Calculate P&L
        entry_value = self.position['position_value']
        exit_value = self.position['quantity'] * price
        pnl = exit_value - entry_value
        pnl_pct = (price / self.position['entry_price'] - 1) * 100
        
        # Update capital
        self.current_capital += pnl
        
        # Record trade
        trade = {
            'entry_time': self.position['entry_time'],
            'exit_time': f"Index_{idx}",
            'entry_price': self.position['entry_price'],
            'exit_price': price,
            'quantity': self.position['quantity'],
            'position_value': self.position['position_value'],
            'pnl': pnl,
            'pnl_pct': pnl_pct,
            'rule_triggered': self.position['rule_triggered'],
            'exit_reason': exit_reason,
            'entry_idx': self.position['entry_idx'],
            'exit_idx': idx,
            'holding_period_minutes': idx - self.position['entry_idx']
        }
        
        trades.append(trade)
        
        # Clear position
        self.position = None
    
    def _calculate_rule_performance(self, trades, rule_name):
        """Calculate performance metrics for a rule"""
        
        if not trades:
            return {
                'total_trades': 0,
                'total_return_pct': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'avg_trade_return': 0,
                'sharpe_ratio': 0,
                'max_drawdown_pct': 0,
            }
        
        trades_df = pd.DataFrame(trades)
        
        # Basic metrics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        win_rate = (winning_trades / total_trades) * 100
        
        # Return metrics
        total_pnl = trades_df['pnl'].sum()
        total_return_pct = (total_pnl / self.initial_capital) * 100
        avg_trade_return = trades_df['pnl_pct'].mean()
        
        # Risk metrics
        winning_pnl = trades_df[trades_df['pnl'] > 0]['pnl'].sum()
        losing_pnl = abs(trades_df[trades_df['pnl'] < 0]['pnl'].sum())
        profit_factor = winning_pnl / losing_pnl if losing_pnl > 0 else float('inf')
        
        # Sharpe ratio (simplified)
        returns_std = trades_df['pnl_pct'].std()
        sharpe_ratio = avg_trade_return / returns_std if returns_std > 0 else 0
        
        # Drawdown
        trades_df['cumulative_pnl'] = trades_df['pnl'].cumsum()
        trades_df['equity_curve'] = self.initial_capital + trades_df['cumulative_pnl']
        trades_df['running_max'] = trades_df['equity_curve'].expanding().max()
        trades_df['drawdown_pct'] = ((trades_df['equity_curve'] - trades_df['running_max']) / trades_df['running_max']) * 100
        max_drawdown_pct = abs(trades_df['drawdown_pct'].min())
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'win_rate': win_rate,
            'total_return_pct': total_return_pct,
            'avg_trade_return': avg_trade_return,
            'profit_factor': profit_factor,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown_pct,
            'total_pnl': total_pnl,
        }
