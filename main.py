"""
Main Runner Script
Comprehensive trading rules analysis system
"""

import sys
import os
from datetime import datetime

from config import Config, QuickTestConfig, FullAnalysisConfig, ResearchConfig, IsolatedBuyRulesConfig, GridSearchConfig, SLTPOnlyConfig, FullAnalysisSLTPOnlyConfig, IndependentEvaluationConfig, FullIndependentEvaluationConfig, OptimizedRulesConfig, OptimizedRulesFullConfig
from backtesting_engine import BacktestingEngine
from performance_tracker import PerformanceTracker


def run_comprehensive_analysis(config_class=Config):
    """Run comprehensive analysis of all trading rules"""
    
    print("🚀 UNIFIED TRADING RULES ANALYSIS SYSTEM")
    print("=" * 80)
    print("Objective: Find the best performing buy/sell rules in REAL-WORLD CONDITIONS")
    print("Method: UNIFIED evaluation - all rules tested simultaneously on each candle")
    print("=" * 80)
    print()
    
    # Initialize configuration
    config = config_class()
    
    print(f"📊 Configuration:")
    print(f"   Dataset Size: {config.get_dataset_size() or 'Full Dataset'}")
    print(f"   Initial Capital: ${config.INITIAL_CAPITAL:,}")
    print(f"   Risk Management: {config.STOP_LOSS_PCT}% SL, {config.TAKE_PROFIT_PCT}% TP")
    print(f"   Position Size: {config.POSITION_SIZE_PCT*100}% of capital")
    print(f"   Max Holding: {config.MAX_HOLDING_PERIOD} minutes")
    print()
    
    print(f"🎯 Rule Categories Enabled:")
    for category in config.ENABLED_RULE_CATEGORIES:
        print(f"   ✓ {category}")
    print()
    
    try:
        # Initialize backtesting engine
        print("🔧 Initializing backtesting engine...")
        engine = BacktestingEngine(config)
        
        # Handle dataset size - if None, use full dataset
        dataset_size = config.get_dataset_size()
        if dataset_size is None:
            end_idx = len(engine.df)
        else:
            end_idx = min(300 + dataset_size, len(engine.df))

        # Get risk configurations for SL/TP testing
        risk_configs = config.get_risk_configs()

        if len(risk_configs) == 1:
            # Single SL/TP configuration
            risk_config = risk_configs[0]

            # Choose evaluation method based on configuration
            if config.USE_INDEPENDENT_EVALUATION:
                print("🧪 Running independent backtest with single SL/TP configuration...")
                unified_results = engine._run_independent_backtests(
                    start_idx=300,
                    end_idx=end_idx,
                    stop_loss_pct=risk_config['stop_loss'],
                    take_profit_pct=risk_config['take_profit']
                )
            else:
                print("🧪 Running unified backtest with single SL/TP configuration...")
                unified_results = engine._run_unified_backtest(
                    start_idx=300,
                    end_idx=end_idx,
                    stop_loss_pct=risk_config['stop_loss'],
                    take_profit_pct=risk_config['take_profit']
                )
        else:
            # Multiple SL/TP configurations - grid search
            print(f"🔬 Running SL/TP grid search with {len(risk_configs)} configurations...")
            all_results = {}

            for i, risk_config in enumerate(risk_configs, 1):
                print(f"   Testing config {i}/{len(risk_configs)}: {risk_config['name']} (SL: {risk_config['stop_loss']}%, TP: {risk_config['take_profit']}%)")

                # Choose evaluation method based on configuration (same logic as single config)
                if config.USE_INDEPENDENT_EVALUATION:
                    print(f"      🧪 Running independent backtest for {risk_config['name']}...")
                    result = engine._run_independent_backtests(
                        start_idx=300,
                        end_idx=end_idx,
                        stop_loss_pct=risk_config['stop_loss'],
                        take_profit_pct=risk_config['take_profit']
                    )
                else:
                    print(f"      🧪 Running unified backtest for {risk_config['name']}...")
                    result = engine._run_unified_backtest(
                        start_idx=300,
                        end_idx=end_idx,
                        stop_loss_pct=risk_config['stop_loss'],
                        take_profit_pct=risk_config['take_profit']
                    )

                all_results[risk_config['name']] = {
                    'config': risk_config,
                    'results': result
                }

                print(f"      Result: {result['total_trades']} trades, {result['total_return_pct']:.2f}% return")

            # Use the best performing configuration for main analysis
            # Handle different result formats from independent vs unified evaluation
            if config.USE_INDEPENDENT_EVALUATION:
                # Independent evaluation returns different format
                best_config_name = max(all_results.keys(),
                                     key=lambda k: all_results[k]['results'].get('system_performance', {}).get('total_return_pct', 0))
                unified_results = all_results[best_config_name]['results']
                best_return = unified_results.get('system_performance', {}).get('total_return_pct', 0)
            else:
                # Unified evaluation format
                best_config_name = max(all_results.keys(), key=lambda k: all_results[k]['results']['total_return_pct'])
                unified_results = all_results[best_config_name]['results']
                best_return = unified_results['total_return_pct']

            print(f"   🏆 Best configuration: {best_config_name} with {best_return:.2f}% return")

            # Save grid search results - handle different result formats
            if config.USE_INDEPENDENT_EVALUATION:
                # Independent evaluation format
                summary = {}
                for name, data in all_results.items():
                    sys_perf = data['results'].get('system_performance', {})
                    summary[name] = {
                        'total_return_pct': sys_perf.get('total_return_pct', 0),
                        'total_trades': sys_perf.get('total_trades', 0),
                        'win_rate': sys_perf.get('win_rate', 0)
                    }
            else:
                # Unified evaluation format
                summary = {name: {'total_return_pct': data['results']['total_return_pct'],
                                'total_trades': data['results']['total_trades'],
                                'win_rate': data['results'].get('win_rate', 0)}
                         for name, data in all_results.items()}

            grid_search_results = {
                'all_configs': all_results,
                'best_config': best_config_name,
                'summary': summary
            }

            # Add grid search results to the main results
            unified_results['grid_search_results'] = grid_search_results

        # Wrap in expected format for performance tracker
        backtest_results = {
            'unified_results': unified_results,
            'backtest_info': {
                'start_time': datetime.now().isoformat(),
                'start_idx': 300,
                'end_idx': end_idx,
                'total_candles': end_idx - 300,
                'dataset_size': len(engine.df),
                'initial_capital': config.INITIAL_CAPITAL,
                'config_used': {
                    'stop_loss_pct': config.STOP_LOSS_PCT,
                    'take_profit_pct': config.TAKE_PROFIT_PCT,
                    'dataset_size': config.get_dataset_size()
                }
            }
        }
        
        # Analyze performance
        print("📈 Analyzing performance...")
        tracker = PerformanceTracker(config)
        analysis_results = tracker.analyze_results(backtest_results)
        
        print("\n✅ COMPREHENSIVE ANALYSIS COMPLETED!")
        print("Check the results/ directory for detailed output files.")
        
        return analysis_results
        
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def run_quick_test():
    """Run quick test with limited dataset"""
    print("🧪 RUNNING QUICK TEST MODE")
    print("=" * 50)
    return run_comprehensive_analysis(QuickTestConfig)


def run_full_analysis():
    """Run full analysis on complete dataset"""
    print("📊 RUNNING FULL ANALYSIS MODE")
    print("=" * 50)
    return run_comprehensive_analysis(FullAnalysisConfig)


def run_research_mode():
    """Run research mode with detailed tracking"""
    print("🔬 RUNNING RESEARCH MODE")
    print("=" * 50)
    return run_comprehensive_analysis(ResearchConfig)


def run_isolated_buy_rules():
    """Run analysis with buy rules isolated from sell rule interference"""
    print("🎯 RUNNING ISOLATED BUY RULES ANALYSIS")
    print("=" * 50)
    print("🚫 Buy signals will be blocked when sell conditions are active")
    print("📊 This isolates buy rule performance from sell rule interference")
    return run_comprehensive_analysis(IsolatedBuyRulesConfig)


def run_grid_search():
    """Run comprehensive SL/TP grid search"""
    print("🔬 RUNNING SL/TP GRID SEARCH")
    print("=" * 50)
    print("📊 Testing multiple SL/TP configurations automatically")
    return run_comprehensive_analysis(GridSearchConfig)


def run_sltp_only():
    """Run analysis with only SL/TP exits (no sell rules)"""
    print("🎯 RUNNING SL/TP ONLY ANALYSIS")
    print("=" * 50)
    print("🚫 All sell rules disabled - exits handled only by SL/TP")
    print("📊 This tests pure buy rule + SL/TP performance")
    return run_comprehensive_analysis(SLTPOnlyConfig)


def run_full_sltp_only():
    """Run full dataset analysis with only SL/TP exits (no sell rules)"""
    print("🎯 RUNNING FULL DATASET SL/TP ONLY ANALYSIS")
    print("=" * 50)
    print("🚫 All sell rules disabled - exits handled only by SL/TP")
    print("📊 This tests pure buy rule + SL/TP performance on full dataset")
    return run_comprehensive_analysis(FullAnalysisSLTPOnlyConfig)


def run_independent_evaluation():
    """Run independent evaluation of buy rules"""
    print("🔬 RUNNING INDEPENDENT BUY RULE EVALUATION")
    print("=" * 50)
    print("🎯 Each buy rule tested in complete isolation")
    print("📊 Prevents duplicate trades and measures true individual performance")
    print("⚡ Includes real-time progress tracking with tqdm")
    return run_comprehensive_analysis(IndependentEvaluationConfig)


def run_full_independent_evaluation():
    """Run independent evaluation on full dataset"""
    print("🔬 RUNNING FULL DATASET INDEPENDENT EVALUATION")
    print("=" * 50)
    print("🎯 Each buy rule tested in complete isolation on full dataset")
    print("📊 Prevents duplicate trades and measures true individual performance")
    print("⚡ Includes real-time progress tracking with tqdm")
    return run_comprehensive_analysis(FullIndependentEvaluationConfig)


def run_optimized_rules():
    """Run optimized rules evaluation on quick dataset"""
    print("🚀 RUNNING OPTIMIZED RULES EVALUATION MODE")
    print("=" * 50)
    print("🎯 Testing ONLY the 48 optimized rules from selected_buy_rules_optimized.py")
    print("📊 Dataset: Quick test (10k candles)")
    print("⚡ Method: Independent evaluation (each rule tested separately)")
    print("🔥 Performance: 3-5x faster execution with identical logic")
    return run_comprehensive_analysis(OptimizedRulesConfig)


def run_optimized_rules_full():
    """Run optimized rules evaluation on full dataset"""
    print("🚀 RUNNING OPTIMIZED RULES FULL EVALUATION MODE")
    print("=" * 50)
    print("🎯 Testing ONLY the 48 optimized rules from selected_buy_rules_optimized.py")
    print("📊 Dataset: Full dataset")
    print("⚡ Method: Independent evaluation (each rule tested separately)")
    print("🔥 Performance: 3-5x faster execution with identical logic")
    print("📈 Filters: Higher standards for full dataset evaluation")
    return run_comprehensive_analysis(OptimizedRulesFullConfig)


def check_data_file():
    """Check if data file exists and is readable"""
    
    config = Config()
    data_file = config.DATA_FILE
    
    print(f"📊 CHECKING DATA FILE: {data_file}")
    print("-" * 50)
    
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        print("Please ensure the Bitcoin data file is in the current directory")
        return False
    
    try:
        import pandas as pd
        df = pd.read_csv(data_file)
        
        print(f"✅ Data file loaded successfully")
        print(f"   Rows: {len(df):,}")
        print(f"   Columns: {list(df.columns)}")
        
        if len(df) > 0:
            print(f"   First row: {df.iloc[0].to_dict()}")
            print(f"   Last row: {df.iloc[-1].to_dict()}")
        
        # Check required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"❌ Missing required columns: {missing_cols}")
            return False
        else:
            print(f"✅ All required columns present")
            return True
            
    except Exception as e:
        print(f"❌ Error reading data file: {str(e)}")
        return False


def print_system_info():
    """Print system information and capabilities"""
    
    print("📋 SYSTEM INFORMATION")
    print("=" * 60)
    print()
    
    print("🎯 OBJECTIVES:")
    print("   • Find the best performing buy/sell rules")
    print("   • Test rules across multiple categories")
    print("   • Apply rigorous filtering criteria")
    print("   • Generate comprehensive performance reports")
    print()
    
    print("📊 RULE CATEGORIES:")
    print("   • ORIGINAL: Rules from previous analysis (30 buy, 15 sell)")
    print("   • PROFESSIONAL: Institutional trading strategies (10 buy, 5 sell)")
    print("   • ACADEMIC: Research-based strategies (5 buy)")
    print("   • AI_GENERATED: Advanced AI-generated rules (10 buy)")
    print()
    
    print("🔬 ANALYSIS FEATURES:")
    print("   • Individual rule backtesting")
    print("   • Comprehensive performance metrics")
    print("   • Rule filtering and ranking")
    print("   • Category-based analysis")
    print("   • Detailed reporting (JSON + Markdown)")
    print()
    
    print("⚙️ CONFIGURATION OPTIONS:")
    print("   • Quick Test: 100k candles for fast iteration")
    print("   • Full Analysis: Complete 4M+ candle dataset")
    print("   • Research Mode: Detailed tracking and analysis")
    print("   • Custom: Configurable parameters")
    print()
    
    print("📈 PERFORMANCE METRICS:")
    print("   • Total return, win rate, profit factor")
    print("   • Sharpe ratio, maximum drawdown")
    print("   • Trade frequency and signal analysis")
    print("   • Risk-adjusted performance scoring")
    print()


def main():
    """Main execution function"""
    
    print_system_info()
    
    # Check data file first
    if not check_data_file():
        print("\n❌ Cannot proceed without valid data file")
        return
    
    print("\n" + "=" * 80)
    
    # Run analysis based on command line arguments or default
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()

        if mode == 'quick':
            analyzer = run_quick_test()
        elif mode == 'full':
            analyzer = run_full_analysis()
        elif mode == 'research':
            analyzer = run_research_mode()
        elif mode == 'isolated':
            analyzer = run_isolated_buy_rules()
        elif mode == 'grid':
            analyzer = run_grid_search()
        elif mode == 'sltp':
            analyzer = run_sltp_only()
        elif mode == 'fullsltp':
            analyzer = run_full_sltp_only()
        elif mode == 'independent':
            analyzer = run_independent_evaluation()
        elif mode == 'fullindependent':
            analyzer = run_full_independent_evaluation()
        elif mode == 'optimized':
            analyzer = run_optimized_rules()
        elif mode == 'optimizedfull':
            analyzer = run_optimized_rules_full()
        else:
            print(f"Unknown mode: {mode}")
            print("Available modes: quick, full, research, isolated, grid, sltp, fullsltp, independent, fullindependent, optimized, optimizedfull")
            return
    else:
        # Default: run quick test
        analyzer = run_quick_test()
    
    if analyzer:
        print("\n🎉 Analysis completed successfully!")
        print("📁 Check the results/ directory for detailed output files")
        print("📊 Review the summary report for key findings")
    else:
        print("\n❌ Analysis failed. Please check the error messages above.")


if __name__ == "__main__":
    main()


# Usage examples:
"""
# Quick test (100k candles)
python main.py quick

# Full analysis (entire dataset)
python main.py full

# Research mode (detailed tracking with SL/TP grid search)
python main.py research

# Isolated buy rules (block buys when sell conditions active)
python main.py isolated

# SL/TP grid search (test multiple SL/TP configurations)
python main.py grid

# SL/TP only (disable all sell rules, use only SL/TP for exits)
python main.py sltp

# Full dataset SL/TP only (disable all sell rules on full dataset)
python main.py fullsltp

# Independent evaluation (each rule tested in isolation)
python main.py independent

# Full dataset independent evaluation (each rule tested in isolation on full dataset)
python main.py fullindependent

# Optimized rules evaluation (ONLY the 48 optimized rules - quick test)
python main.py optimized

# Optimized rules full evaluation (ONLY the 48 optimized rules - full dataset)
python main.py optimizedfull

# Default (quick test)
python main.py
"""
