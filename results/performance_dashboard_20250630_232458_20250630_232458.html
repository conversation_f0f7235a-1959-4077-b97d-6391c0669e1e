
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 11 Top-Performing Buy Rules</p>
        <p>Generated: 2025-06-30 23:24:58</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">11</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">25.0%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">8.1%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">24.6%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">65.2%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">3,344</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["24.6%","16.4%","12.9%","6.4%","8.5%","4.3%","6.0%","2.1%","2.5%","4.0%"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 3: Smart Money Flow Divergence","AI Rule 8: Momentum Divergence Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Ext Rule 5: ATR Volatility Expansion","Professional Rule 10: CCI Reversal Enhanced","Volume Rule 5: Smart Money Volume"],"y":[24.579618658694475,16.414919550150017,12.864375041948945,6.435196896376897,8.523256613923943,4.256972774286205,6.036065253292567,2.0655525638317993,2.532375890002131,4.006639880730436],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 3: Smart Money Flow Divergence","AI Rule 8: Momentum Divergence Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Ext Rule 5: ATR Volatility Expansion","Professional Rule 10: CCI Reversal Enhanced","Volume Rule 5: Smart Money Volume"],"y":[607,448,445,157,239,75,64,66,37,23],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 3: Smart Money Flow Divergence","AI Rule 8: Momentum Divergence Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Ext Rule 5: ATR Volatility Expansion","Professional Rule 10: CCI Reversal Enhanced","Volume Rule 5: Smart Money Volume"],"y":[329,241,238,80,138,39,35,45,17,10],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[24.579618658694475,16.414919550150017,12.864375041948945,6.435196896376897,8.523256613923943,4.256972774286205,6.036065253292567,2.0655525638317993,2.532375890002131,4.006639880730436,1.0448314059367696],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["AI Rule 10","Rule 7","Ext Rule 6","Prof Rule 7","AI Rule 3","AI Rule 8","Professional Rule 7","Ext Rule 5","Professional Rule 10","Volume Rule 5","Advanced Rule 7"],"textposition":"top center","x":[13.085221764361071,7.245674218202655,15.301980487491306,7.310542794359988,7.027466181420065,5.915002580780046,4.537011721561604,3.5993168137823996,2.6729751920430598,1.7219445729324563,0.788102219429141],"y":[24.579618658694475,16.414919550150017,12.864375041948945,6.435196896376897,8.523256613923943,4.256972774286205,6.036065253292567,2.0655525638317993,2.532375890002131,4.006639880730436,1.0448314059367696],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["12.5%","16.4%","4.8%","6.4%"],"textposition":"auto","x":["AI_GENERATED","ORIGINAL","UNKNOWN","PROFESSIONAL"],"y":[12.453282682301541,16.414919550150017,4.758306672623775,6.435196896376897],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["936","689","683","237","377","114","99","111","54","33"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 3: Smart Money Flow Divergence","AI Rule 8: Momentum Divergence Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Ext Rule 5: ATR Volatility Expansion","Professional Rule 10: CCI Reversal Enhanced","Volume Rule 5: Smart Money Volume"],"y":[936,689,683,237,377,114,99,111,54,33],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936],"y":[0,0.026260276344759057,0.052520552689518114,0.07878082903427716,0.10504110537903623,0.13130138172379527,0.15756165806855432,0.18382193441331338,0.21008221075807246,0.2363424871028315,0.26260276344759054,0.2888630397923496,0.31512331613710864,0.34138359248186767,0.36764386882662675,0.3939041451713858,0.4201644215161449,0.44642469786090394,0.472684974205663,0.49894525055042205,0.5252055268951811,0.5514658032399401,0.5777260795846992,0.6039863559294583,0.6302466322742173,0.6565069086189763,0.6827671849637353,0.7090274613084945,0.7352877376532535,0.7615480139980126,0.7878082903427716,0.8140685666875307,0.8403288430322898,0.8665891193770487,0.8928493957218079,0.9191096720665669,0.945369948411326,0.971630224756085,0.9978905011008441,1.0241507774456031,1.0504110537903621,1.0766713301351212,1.1029316064798802,1.1291918828246394,1.1554521591693985,1.1817124355141575,1.2079727118589165,1.2342329882036756,1.2604932645484346,1.2867535408931936,1.3130138172379526,1.3392740935827119,1.3655343699274707,1.39179464627223,1.418054922616989,1.444315198961748,1.470575475306507,1.496835751651266,1.5230960279960253,1.549356304340784,1.5756165806855431,1.6018768570303024,1.6281371333750614,1.6543974097198204,1.6806576860645797,1.7069179624093387,1.7331782387540975,1.7594385150988565,1.7856987914436158,1.8119590677883748,1.8382193441331338,1.8644796204778926,1.890739896822652,1.917000173167411,1.94326044951217,1.9695207258569292,1.9957810022016882,2.022041278546447,2.0483015548912062,2.0745618312359655,2.1008221075807243,2.1270823839254835,2.1533426602702423,2.1796029366150016,2.2058632129597604,2.2321234893045196,2.258383765649279,2.2846440419940377,2.310904318338797,2.3371645946835558,2.363424871028315,2.389685147373074,2.415945423717833,2.442205700062592,2.468465976407351,2.4947262527521104,2.520986529096869,2.5472468054416284,2.573507081786387,2.5997673581311465,2.6260276344759053,2.6522879108206645,2.6785481871654238,2.7048084635101826,2.7310687398549414,2.7573290161997006,2.78358929254446,2.8098495688892187,2.836109845233978,2.862370121578737,2.888630397923496,2.9148906742682548,2.941150950613014,2.9674112269577733,2.993671503302532,3.0199317796472913,3.0461920559920506,3.0724523323368094,3.098712608681568,3.1249728850263274,3.1512331613710862,3.177493437715846,3.2037537140606047,3.230013990405364,3.2562742667501228,3.2825345430948816,3.308794819439641,3.3350550957843996,3.3613153721291593,3.387575648473918,3.4138359248186774,3.440096201163436,3.466356477508195,3.4926167538529542,3.518877030197713,3.545137306542472,3.5713975828872315,3.5976578592319908,3.6239181355767496,3.6501784119215084,3.6764386882662676,3.7026989646110264,3.7289592409557852,3.755219517300545,3.781479793645304,3.807740069990063,3.834000346334822,3.860260622679581,3.88652089902434,3.9127811753690986,3.9390414517138583,3.9653017280586176,3.9915620044033764,4.017822280748136,4.044082557092894,4.070342833437653,4.0966031097824125,4.122863386127171,4.149123662471931,4.17538393881669,4.201644215161449,4.227904491506208,4.254164767850967,4.2804250441957254,4.306685320540485,4.332945596885244,4.359205873230003,4.385466149574762,4.411726425919521,4.43798670226428,4.464246978609039,4.490507254953798,4.516767531298558,4.543027807643317,4.569288083988075,4.595548360332835,4.621808636677594,4.648068913022352,4.6743291893671115,4.700589465711871,4.72684974205663,4.753110018401389,4.779370294746148,4.805630571090907,4.831890847435666,4.8581511237804245,4.884411400125184,4.910671676469944,4.936931952814702,4.9631922291594615,4.989452505504221,5.015712781848979,5.041973058193738,5.068233334538498,5.094493610883257,5.120753887228016,5.147014163572774,5.173274439917534,5.199534716262293,5.225794992607051,5.2520552689518105,5.27831554529657,5.304575821641329,5.330836097986088,5.3570963743308475,5.383356650675606,5.409616927020365,5.435877203365124,5.462137479709883,5.488397756054643,5.514658032399401,5.5409183087441605,5.56717858508892,5.593438861433678,5.619699137778437,5.645959414123197,5.672219690467956,5.698479966812715,5.724740243157474,5.751000519502233,5.777260795846992,5.803521072191751,5.8297813485365095,5.856041624881269,5.882301901226028,5.908562177570787,5.9348224539155465,5.961082730260305,5.987343006605064,6.013603282949823,6.039863559294583,6.066123835639342,6.092384111984101,6.1186443883288595,6.144904664673619,6.171164941018378,6.197425217363136,6.223685493707896,6.249945770052655,6.276206046397413,6.3024663227421724,6.328726599086932,6.354986875431692,6.381247151776451,6.407507428121209,6.433767704465969,6.460027980810728,6.486288257155486,6.5125485335002455,6.538808809845005,6.565069086189763,6.591329362534522,6.617589638879282,6.64384991522404,6.670110191568799,6.6963704679135585,6.722630744258319,6.748891020603078,6.775151296947836,6.8014115732925955,6.827671849637355,6.853932125982113,6.880192402326872,6.906452678671632,6.93271295501639,6.958973231361149,6.9852335077059085,7.011493784050667,7.037754060395426,7.064014336740185,7.090274613084944,7.116534889429705,7.142795165774463,7.169055442119222,7.1953157184639815,7.22157599480874,7.247836271153499,7.274096547498258,7.300356823843017,7.326617100187776,7.352877376532535,7.379137652877294,7.405397929222053,7.431658205566812,7.4579184819115705,7.48417875825633,7.51043903460109,7.536699310945849,7.562959587290608,7.589219863635367,7.615480139980126,7.641740416324885,7.668000692669644,7.694260969014403,7.720521245359162,7.74678152170392,7.77304179804868,7.799302074393439,7.825562350738197,7.8518226270829565,7.878082903427717,7.904343179772476,7.930603456117235,7.9568637324619935,7.983124008806753,8.009384285151512,8.035644561496271,8.06190483784103,8.088165114185788,8.114425390530547,8.140685666875306,8.166945943220066,8.193206219564825,8.219466495909584,8.245726772254342,8.271987048599103,8.298247324943862,8.324507601288621,8.35076787763338,8.377028153978138,8.403288430322897,8.429548706667656,8.455808983012416,8.482069259357175,8.508329535701934,8.534589812046692,8.560850088391451,8.58711036473621,8.61337064108097,8.639630917425729,8.665891193770488,8.692151470115247,8.718411746460006,8.744672022804766,8.770932299149525,8.797192575494284,8.823452851839042,8.8497131281838,8.87597340452856,8.90223368087332,8.928493957218079,8.954754233562838,8.981014509907595,9.007274786252355,9.033535062597116,9.059795338941875,9.086055615286634,9.112315891631392,9.13857616797615,9.16483644432091,9.19109672066567,9.217356997010429,9.243617273355188,9.269877549699945,9.296137826044705,9.322398102389464,9.348658378734223,9.374918655078982,9.401178931423742,9.4274392077685,9.45369948411326,9.47995976045802,9.506220036802778,9.532480313147538,9.558740589492295,9.585000865837054,9.611261142181814,9.637521418526573,9.663781694871332,9.690041971216091,9.716302247560849,9.742562523905608,9.768822800250367,9.795083076595128,9.821343352939888,9.847603629284645,9.873863905629404,9.900124181974164,9.926384458318923,9.952644734663682,9.978905011008441,10.005165287353199,10.031425563697958,10.057685840042717,10.083946116387477,10.110206392732236,10.136466669076995,10.162726945421753,10.188987221766514,10.215247498111273,10.241507774456032,10.267768050800791,10.294028327145549,10.320288603490308,10.346548879835067,10.372809156179827,10.399069432524586,10.425329708869345,10.451589985214103,10.477850261558862,10.504110537903621,10.53037081424838,10.55663109059314,10.582891366937899,10.609151643282658,10.635411919627417,10.661672195972177,10.687932472316936,10.714192748661695,10.740453025006452,10.766713301351212,10.792973577695971,10.81923385404073,10.84549413038549,10.871754406730249,10.898014683075006,10.924274959419765,10.950535235764526,10.976795512109286,11.003055788454045,11.029316064798802,11.055576341143562,11.081836617488321,11.10809689383308,11.13435717017784,11.160617446522599,11.186877722867356,11.213137999212115,11.239398275556875,11.265658551901634,11.291918828246393,11.318179104591152,11.344439380935912,11.370699657280671,11.39695993362543,11.42322020997019,11.449480486314949,11.475740762659706,11.502001039004465,11.528261315349225,11.554521591693984,11.580781868038743,11.607042144383502,11.633302420728262,11.659562697073019,11.685822973417778,11.712083249762538,11.738343526107299,11.764603802452056,11.790864078796815,11.817124355141575,11.843384631486334,11.869644907831093,11.895905184175852,11.92216546052061,11.948425736865369,11.974686013210128,12.000946289554888,12.027206565899647,12.053466842244406,12.079727118589165,12.105987394933925,12.132247671278684,12.158507947623443,12.184768223968202,12.21102850031296,12.237288776657719,12.263549053002478,12.289809329347237,12.316069605691997,12.342329882036756,12.368590158381515,12.394850434726273,12.421110711071032,12.447370987415791,12.47363126376055,12.49989154010531,12.526151816450069,12.552412092794826,12.578672369139586,12.604932645484345,12.631192921829104,12.657453198173863,12.683713474518624,12.709973750863384,12.736234027208143,12.762494303552902,12.78875457989766,12.815014856242419,12.841275132587178,12.867535408931937,12.893795685276697,12.920055961621456,12.946316237966213,12.972576514310973,12.998836790655732,13.025097067000491,13.05135734334525,13.07761761969001,13.103877896034769,13.130138172379526,13.156398448724286,13.182658725069045,13.208919001413804,13.235179277758563,13.261439554103323,13.28769983044808,13.31396010679284,13.340220383137599,13.366480659482358,13.392740935827117,13.419001212171876,13.445261488516637,13.471521764861397,13.497782041206156,13.524042317550913,13.550302593895672,13.576562870240432,13.602823146585191,13.62908342292995,13.65534369927471,13.681603975619469,13.707864251964226,13.734124528308985,13.760384804653745,13.786645080998504,13.812905357343263,13.839165633688022,13.86542591003278,13.89168618637754,13.917946462722298,13.944206739067058,13.970467015411817,13.996727291756576,14.022987568101334,14.049247844446093,14.075508120790852,14.101768397135611,14.12802867348037,14.15428894982513,14.180549226169887,14.206809502514647,14.23306977885941,14.259330055204167,14.285590331548926,14.311850607893685,14.338110884238445,14.364371160583204,14.390631436927963,14.416891713272722,14.44315198961748,14.469412265962239,14.495672542306998,14.521932818651758,14.548193094996517,14.574453371341276,14.600713647686034,14.626973924030793,14.653234200375552,14.679494476720311,14.70575475306507,14.73201502940983,14.758275305754587,14.784535582099346,14.810795858444106,14.837056134788865,14.863316411133624,14.889576687478383,14.915836963823141,14.9420972401679,14.96835751651266,14.994617792857422,15.02087806920218,15.047138345546939,15.073398621891698,15.099658898236457,15.125919174581217,15.152179450925976,15.178439727270733,15.204700003615493,15.230960279960252,15.257220556305011,15.28348083264977,15.30974110899453,15.336001385339287,15.362261661684046,15.388521938028806,15.414782214373565,15.441042490718324,15.467302767063083,15.49356304340784,15.5198233197526,15.54608359609736,15.572343872442119,15.598604148786878,15.624864425131637,15.651124701476395,15.677384977821154,15.703645254165913,15.729905530510672,15.756165806855433,15.782426083200193,15.808686359544952,15.834946635889711,15.86120691223447,15.88746718857923,15.913727464923987,15.939987741268746,15.966248017613506,15.992508293958265,16.018768570303024,16.04502884664778,16.071289122992543,16.0975493993373,16.12380967568206,16.15006995202682,16.176330228371576,16.202590504716337,16.228850781061094,16.255111057405855,16.281371333750613,16.30763161009537,16.33389188644013,16.36015216278489,16.38641243912965,16.412672715474407,16.43893299181917,16.465193268163926,16.491453544508683,16.517713820853448,16.543974097198205,16.570234373542963,16.596494649887724,16.62275492623248,16.649015202577242,16.675275478922,16.70153575526676,16.72779603161152,16.754056307956276,16.780316584301037,16.806576860645794,16.832837136990555,16.859097413335313,16.88535768968007,16.91161796602483,16.93787824236959,16.96413851871435,16.990398795059107,17.01665907140387,17.042919347748626,17.069179624093383,17.095439900438144,17.121700176782902,17.147960453127663,17.17422072947242,17.20048100581718,17.22674128216194,17.253001558506696,17.279261834851457,17.30552211119622,17.331782387540976,17.358042663885737,17.384302940230494,17.410563216575255,17.436823492920013,17.46308376926477,17.48934404560953,17.51560432195429,17.54186459829905,17.568124874643807,17.59438515098857,17.620645427333326,17.646905703678083,17.673165980022844,17.6994262563676,17.725686532712363,17.75194680905712,17.77820708540188,17.80446736174664,17.830727638091396,17.856987914436157,17.883248190780915,17.909508467125676,17.935768743470433,17.96202901981519,17.98828929615995,18.01454957250471,18.04080984884947,18.06707012519423,18.09333040153899,18.11959067788375,18.145850954228507,18.172111230573268,18.198371506918026,18.224631783262783,18.250892059607544,18.2771523359523,18.303412612297063,18.32967288864182,18.355933164986578,18.38219344133134,18.408453717676096,18.434713994020857,18.460974270365615,18.487234546710376,18.513494823055133,18.53975509939989,18.56601537574465,18.59227565208941,18.61853592843417,18.644796204778928,18.67105648112369,18.697316757468446,18.723577033813203,18.749837310157965,18.776097586502722,18.802357862847483,18.828618139192244,18.854878415537,18.881138691881763,18.90739896822652,18.933659244571277,18.95991952091604,18.986179797260796,19.012440073605557,19.038700349950314,19.064960626295075,19.091220902639833,19.11748117898459,19.14374145532935,19.17000173167411,19.19626200801887,19.222522284363627,19.24878256070839,19.275042837053146,19.301303113397903,19.327563389742664,19.353823666087422,19.380083942432183,19.40634421877694,19.432604495121698,19.45886477146646,19.485125047811216,19.511385324155977,19.537645600500735,19.563905876845496,19.590166153190257,19.616426429535014,19.642686705879775,19.668946982224533,19.69520725856929,19.72146753491405,19.74772781125881,19.77398808760357,19.800248363948327,19.82650864029309,19.852768916637846,19.879029192982603,19.905289469327364,19.931549745672122,19.957810022016883,19.98407029836164,20.010330574706398,20.03659085105116,20.062851127395916,20.089111403740677,20.115371680085435,20.141631956430196,20.167892232774953,20.19415250911971,20.22041278546447,20.24667306180923,20.27293333815399,20.299193614498748,20.325453890843505,20.351714167188266,20.377974443533027,20.404234719877785,20.430494996222546,20.456755272567303,20.483015548912064,20.50927582525682,20.535536101601583,20.56179637794634,20.588056654291098,20.61431693063586,20.640577206980616,20.666837483325377,20.693097759670135,20.719358036014896,20.745618312359653,20.77187858870441,20.79813886504917,20.82439914139393,20.85065941773869,20.876919694083448,20.903179970428205,20.929440246772966,20.955700523117724,20.981960799462485,21.008221075807242,21.034481352152003,21.06074162849676,21.087001904841518,21.11326218118628,21.13952245753104,21.165782733875798,21.19204301022056,21.218303286565316,21.244563562910077,21.270823839254835,21.297084115599596,21.323344391944353,21.34960466828911,21.37586494463387,21.40212522097863,21.42838549732339,21.454645773668148,21.480906050012905,21.507166326357666,21.533426602702423,21.559686879047185,21.585947155391942,21.612207431736703,21.63846770808146,21.664727984426218,21.69098826077098,21.717248537115736,21.743508813460497,21.769769089805255,21.796029366150012,21.822289642494773,21.84854991883953,21.874810195184292,21.901070471529053,21.92733074787381,21.95359102421857,21.97985130056333,22.00611157690809,22.032371853252847,22.058632129597605,22.084892405942366,22.111152682287123,22.137412958631884,22.163673234976642,22.189933511321403,22.21619378766616,22.242454064010918,22.26871434035568,22.294974616700436,22.321234893045197,22.347495169389955,22.373755445734712,22.400015722079473,22.42627599842423,22.452536274768992,22.47879655111375,22.50505682745851,22.531317103803268,22.557577380148025,22.583837656492786,22.610097932837544,22.636358209182305,22.662618485527066,22.688878761871823,22.715139038216584,22.741399314561342,22.767659590906103,22.79391986725086,22.820180143595618,22.84644041994038,22.872700696285136,22.898960972629897,22.925221248974655,22.951481525319412,22.977741801664173,23.00400207800893,23.03026235435369,23.05652263069845,23.08278290704321,23.109043183387968,23.135303459732725,23.161563736077486,23.187824012422244,23.214084288767005,23.240344565111762,23.266604841456523,23.29286511780128,23.319125394146038,23.3453856704908,23.371645946835557,23.397906223180318,23.424166499525075,23.450426775869836,23.476687052214597,23.502947328559355,23.529207604904112,23.555467881248873,23.58172815759363,23.60798843393839,23.63424871028315,23.66050898662791,23.686769262972668,23.713029539317425,23.739289815662186,23.765550092006944,23.791810368351705,23.818070644696462,23.84433092104122,23.87059119738598,23.896851473730738,23.9231117500755,23.949372026420257,23.975632302765018,24.001892579109775,24.028152855454532,24.054413131799294,24.08067340814405,24.106933684488812,24.13319396083357,24.15945423717833,24.185714513523088,24.21197478986785,24.23823506621261,24.264495342557368,24.290755618902125,24.317015895246886,24.343276171591643,24.369536447936404,24.395796724281162,24.42205700062592,24.44831727697068,24.474577553315438,24.5008378296602,24.527098106004956,24.553358382349717,24.579618658694475],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689],"y":[0,0.023824266400798284,0.04764853280159657,0.07147279920239484,0.09529706560319313,0.11912133200399141,0.14294559840478968,0.16676986480558798,0.19059413120638627,0.21441839760718456,0.23824266400798283,0.2620669304087811,0.28589119680957936,0.3097154632103777,0.33353972961117595,0.35736399601197427,0.38118826241277254,0.4050125288135708,0.4288367952143691,0.4526610616151674,0.47648532801596566,0.5003095944167639,0.5241338608175622,0.5479581272183605,0.5717823936191587,0.5956066600199571,0.6194309264207554,0.6432551928215536,0.6670794592223519,0.6909037256231502,0.7147279920239485,0.7385522584247468,0.7623765248255451,0.7862007912263433,0.8100250576271416,0.83384932402794,0.8576735904287383,0.8814978568295365,0.9053221232303348,0.929146389631133,0.9529706560319313,0.9767949224327297,1.0006191888335279,1.0244434552343262,1.0482677216351244,1.0720919880359228,1.095916254436721,1.1197405208375193,1.1435647872383174,1.167389053639116,1.1912133200399142,1.2150375864407126,1.2388618528415107,1.2626861192423091,1.2865103856431073,1.3103346520439056,1.3341589184447038,1.3579831848455022,1.3818074512463003,1.4056317176470987,1.429455984047897,1.4532802504486955,1.4771045168494936,1.500928783250292,1.5247530496510902,1.5485773160518885,1.5724015824526867,1.5962258488534848,1.6200501152542832,1.6438743816550814,1.66769864805588,1.6915229144566781,1.7153471808574765,1.7391714472582747,1.762995713659073,1.7868199800598712,1.8106442464606696,1.8344685128614677,1.858292779262266,1.8821170456630643,1.9059413120638626,1.929765578464661,1.9535898448654594,1.9774141112662575,2.0012383776670557,2.025062644067854,2.0488869104686525,2.0727111768694506,2.0965354432702488,2.1203597096710474,2.1441839760718455,2.1680082424726437,2.191832508873442,2.2156567752742404,2.2394810416750386,2.2633053080758367,2.287129574476635,2.310953840877434,2.334778107278232,2.3586023736790303,2.3824266400798284,2.4062509064806266,2.430075172881425,2.4538994392822233,2.4777237056830215,2.5015479720838196,2.5253722384846182,2.5491965048854164,2.5730207712862145,2.5968450376870127,2.6206693040878113,2.6444935704886094,2.6683178368894076,2.6921421032902058,2.7159663696910044,2.7397906360918025,2.7636149024926007,2.787439168893399,2.8112634352941974,2.835087701694996,2.858911968095794,2.8827362344965923,2.906560500897391,2.930384767298189,2.9542090336989872,2.9780333000997854,3.001857566500584,3.025681832901382,3.0495060993021803,3.0733303657029785,3.097154632103777,3.120978898504575,3.1448031649053734,3.1686274313061715,3.1924516977069697,3.2162759641077683,3.2401002305085664,3.2639244969093646,3.2877487633101627,3.3115730297109613,3.33539729611176,3.359221562512558,3.3830458289133563,3.406870095314155,3.430694361714953,3.454518628115751,3.4783428945165493,3.502167160917348,3.525991427318146,3.5498156937189442,3.5736399601197424,3.597464226520541,3.621288492921339,3.6451127593221373,3.6689370257229355,3.692761292123734,3.716585558524532,3.7404098249253304,3.7642340913261285,3.788058357726927,3.8118826241277253,3.8357068905285234,3.859531156929322,3.8833554233301206,3.907179689730919,3.931003956131717,3.954828222532515,3.9786524889333132,4.002476755334111,4.02630102173491,4.050125288135708,4.073949554536506,4.097773820937305,4.121598087338103,4.145422353738901,4.1692466201397,4.1930708865404975,4.216895152941296,4.240719419342095,4.264543685742892,4.288367952143691,4.312192218544489,4.336016484945287,4.359840751346086,4.383665017746884,4.407489284147682,4.431313550548481,4.455137816949279,4.478962083350077,4.502786349750875,4.5266106161516735,4.550434882552472,4.57425914895327,4.598083415354068,4.621907681754868,4.645731948155666,4.669556214556464,4.693380480957262,4.7172047473580605,4.741029013758859,4.764853280159657,4.788677546560455,4.812501812961253,4.836326079362052,4.86015034576285,4.883974612163648,4.907798878564447,4.931623144965245,4.955447411366043,4.9792716777668415,5.003095944167639,5.026920210568438,5.0507444769692365,5.074568743370034,5.098393009770833,5.122217276171631,5.146041542572429,5.169865808973228,5.193690075374025,5.217514341774824,5.241338608175623,5.26516287457642,5.288987140977219,5.3128114073780175,5.336635673778815,5.360459940179614,5.3842842065804115,5.40810847298121,5.431932739382009,5.455757005782806,5.479581272183605,5.503405538584403,5.527229804985201,5.551054071386,5.574878337786798,5.598702604187596,5.622526870588395,5.6463511369891926,5.670175403389992,5.69399966979079,5.717823936191588,5.741648202592387,5.765472468993185,5.789296735393983,5.813121001794782,5.83694526819558,5.860769534596378,5.884593800997176,5.9084180673979745,5.932242333798773,5.956066600199571,5.979890866600369,6.003715133001168,6.027539399401966,6.051363665802764,6.075187932203562,6.099012198604361,6.122836465005159,6.146660731405957,6.1704849978067555,6.194309264207554,6.218133530608352,6.24195779700915,6.265782063409948,6.289606329810747,6.313430596211545,6.337254862612343,6.361079129013142,6.384903395413939,6.408727661814738,6.432551928215537,6.456376194616334,6.480200461017133,6.5040247274179315,6.527848993818729,6.551673260219528,6.5754975266203255,6.599321793021124,6.623146059421923,6.64697032582272,6.67079459222352,6.6946188586243185,6.718443125025116,6.742267391425915,6.7660916578267125,6.789915924227511,6.81374019062831,6.837564457029107,6.861388723429906,6.885212989830705,6.909037256231502,6.932861522632301,6.956685789033099,6.980510055433897,7.004334321834696,7.0281585882354936,7.051982854636292,7.07580712103709,7.0996313874378885,7.123455653838687,7.147279920239485,7.171104186640283,7.194928453041082,7.21875271944188,7.242576985842678,7.266401252243476,7.290225518644275,7.314049785045073,7.337874051445871,7.3616983178466695,7.385522584247468,7.409346850648266,7.433171117049064,7.456995383449862,7.480819649850661,7.504643916251459,7.528468182652257,7.552292449053056,7.576116715453854,7.599940981854652,7.6237652482554505,7.647589514656248,7.671413781057047,7.695238047457846,7.719062313858644,7.742886580259443,7.766710846660241,7.790535113061039,7.814359379461838,7.838183645862635,7.862007912263434,7.8858321786642325,7.90965644506503,7.933480711465829,7.9573049778666265,7.981129244267425,8.004953510668223,8.02877777706902,8.05260204346982,8.076426309870618,8.100250576271415,8.124074842672215,8.147899109073013,8.17172337547381,8.19554764187461,8.219371908275408,8.243196174676205,8.267020441077005,8.290844707477802,8.3146689738786,8.3384932402794,8.362317506680197,8.386141773080995,8.409966039481795,8.433790305882592,8.45761457228339,8.48143883868419,8.505263105084987,8.529087371485785,8.552911637886584,8.576735904287382,8.60056017068818,8.624384437088978,8.648208703489777,8.672032969890575,8.695857236291372,8.719681502692172,8.74350576909297,8.767330035493767,8.791154301894567,8.814978568295365,8.838802834696162,8.862627101096962,8.88645136749776,8.910275633898557,8.934099900299355,8.957924166700154,8.981748433100952,9.00557269950175,9.02939696590255,9.053221232303347,9.077045498704145,9.100869765104944,9.124694031505742,9.14851829790654,9.172342564307339,9.196166830708137,9.219991097108934,9.243815363509736,9.267639629910533,9.291463896311331,9.315288162712129,9.339112429112928,9.362936695513726,9.386760961914524,9.410585228315323,9.434409494716121,9.458233761116919,9.482058027517718,9.505882293918516,9.529706560319314,9.553530826720113,9.57735509312091,9.601179359521709,9.625003625922506,9.648827892323306,9.672652158724103,9.696476425124901,9.7203006915257,9.744124957926498,9.767949224327296,9.791773490728096,9.815597757128893,9.839422023529691,9.86324628993049,9.887070556331288,9.910894822732086,9.934719089132885,9.958543355533683,9.98236762193448,10.006191888335279,10.030016154736078,10.053840421136876,10.077664687537673,10.101488953938473,10.12531322033927,10.149137486740068,10.172961753140868,10.196786019541666,10.220610285942463,10.244434552343263,10.26825881874406,10.292083085144858,10.315907351545656,10.339731617946455,10.363555884347253,10.38738015074805,10.41120441714885,10.435028683549648,10.458852949950446,10.482677216351245,10.506501482752043,10.53032574915284,10.55415001555364,10.577974281954438,10.601798548355235,10.625622814756035,10.649447081156833,10.67327134755763,10.697095613958428,10.720919880359228,10.744744146760025,10.768568413160823,10.792392679561623,10.81621694596242,10.840041212363218,10.863865478764017,10.887689745164815,10.911514011565613,10.935338277966412,10.95916254436721,10.982986810768008,11.006811077168805,11.030635343569605,11.054459609970403,11.0782838763712,11.102108142772,11.125932409172798,11.149756675573595,11.173580941974395,11.197405208375192,11.22122947477599,11.24505374117679,11.268878007577587,11.292702273978385,11.316526540379186,11.340350806779984,11.364175073180782,11.38799933958158,11.411823605982379,11.435647872383177,11.459472138783974,11.483296405184774,11.507120671585572,11.53094493798637,11.554769204387169,11.578593470787967,11.602417737188764,11.626242003589564,11.650066269990361,11.67389053639116,11.697714802791959,11.721539069192756,11.745363335593554,11.769187601994352,11.793011868395151,11.816836134795949,11.840660401196747,11.864484667597546,11.888308933998344,11.912133200399142,11.935957466799941,11.959781733200739,11.983605999601536,12.007430266002336,12.031254532403134,12.055078798803931,12.07890306520473,12.102727331605529,12.126551598006326,12.150375864407124,12.174200130807924,12.198024397208721,12.221848663609519,12.245672930010318,12.269497196411116,12.293321462811914,12.317145729212713,12.340969995613511,12.364794262014309,12.388618528415108,12.412442794815906,12.436267061216704,12.460091327617501,12.4839155940183,12.507739860419099,12.531564126819896,12.555388393220696,12.579212659621493,12.603036926022291,12.62686119242309,12.650685458823888,12.674509725224686,12.698333991625486,12.722158258026283,12.745982524427081,12.769806790827879,12.793631057228678,12.817455323629476,12.841279590030274,12.865103856431073,12.88892812283187,12.912752389232669,12.936576655633468,12.960400922034266,12.984225188435063,13.008049454835863,13.03187372123666,13.055697987637458,13.079522254038258,13.103346520439056,13.127170786839853,13.150995053240651,13.17481931964145,13.198643586042248,13.222467852443046,13.246292118843845,13.270116385244643,13.29394065164544,13.317764918046242,13.34158918444704,13.365413450847837,13.389237717248637,13.413061983649435,13.436886250050232,13.46071051645103,13.48453478285183,13.508359049252627,13.532183315653425,13.556007582054225,13.579831848455022,13.60365611485582,13.62748038125662,13.651304647657417,13.675128914058215,13.698953180459014,13.722777446859812,13.74660171326061,13.77042597966141,13.794250246062207,13.818074512463005,13.841898778863802,13.865723045264602,13.8895473116654,13.913371578066197,13.937195844466997,13.961020110867794,13.984844377268592,14.008668643669392,14.03249291007019,14.056317176470987,14.080141442871787,14.103965709272584,14.127789975673382,14.15161424207418,14.17543850847498,14.199262774875777,14.223087041276575,14.246911307677374,14.270735574078172,14.29455984047897,14.318384106879769,14.342208373280567,14.366032639681364,14.389856906082164,14.413681172482962,14.43750543888376,14.461329705284559,14.485153971685357,14.508978238086154,14.532802504486952,14.556626770887751,14.58045103728855,14.604275303689347,14.628099570090146,14.651923836490944,14.675748102891742,14.699572369292541,14.723396635693339,14.747220902094137,14.771045168494936,14.794869434895734,14.818693701296532,14.842517967697331,14.866342234098129,14.890166500498927,14.913990766899724,14.937815033300524,14.961639299701321,14.98546356610212,15.009287832502919,15.033112098903716,15.056936365304514,15.080760631705314,15.104584898106111,15.128409164506909,15.152233430907708,15.176057697308506,15.199881963709304,15.223706230110102,15.247530496510901,15.271354762911699,15.295179029312497,15.319003295713296,15.342827562114094,15.366651828514891,15.390476094915693,15.41430036131649,15.438124627717288,15.461948894118088,15.485773160518885,15.509597426919683,15.533421693320483,15.55724595972128,15.581070226122078,15.604894492522876,15.628718758923675,15.652543025324473,15.67636729172527,15.70019155812607,15.724015824526868,15.747840090927665,15.771664357328465,15.795488623729263,15.81931289013006,15.84313715653086,15.866961422931658,15.890785689332455,15.914609955733253,15.938434222134052,15.96225848853485,15.986082754935648,16.009907021336446,16.033731287737243,16.05755555413804,16.081379820538842,16.10520408693964,16.129028353340438,16.152852619741235,16.176676886142033,16.20050115254283,16.224325418943632,16.24814968534443,16.271973951745228,16.295798218146025,16.319622484546823,16.34344675094762,16.36727101734842,16.39109528374922,16.414919550150017],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Ext Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683],"y":[0,0.018835102550437695,0.03767020510087539,0.056505307651313084,0.07534041020175078,0.09417551275218847,0.11301061530262617,0.13184571785306387,0.15068082040350156,0.16951592295393925,0.18835102550437693,0.20718612805481462,0.22602123060525234,0.24485633315569003,0.26369143570612774,0.28252653825656543,0.3013616408070031,0.3201967433574408,0.3390318459078785,0.3578669484583162,0.37670205100875387,0.39553715355919156,0.41437225610962924,0.433207358660067,0.4520424612105047,0.47087756376094236,0.48971266631138005,0.5085477688618177,0.5273828714122555,0.5462179739626931,0.5650530765131309,0.5838881790635685,0.6027232816140062,0.6215583841644439,0.6403934867148816,0.6592285892653192,0.678063691815757,0.6968987943661947,0.7157338969166324,0.7345689994670701,0.7534041020175077,0.7722392045679455,0.7910743071183831,0.8099094096688209,0.8287445122192585,0.8475796147696962,0.866414717320134,0.8852498198705716,0.9040849224210094,0.922920024971447,0.9417551275218847,0.9605902300723224,0.9794253326227601,0.9982604351731977,1.0170955377236355,1.0359306402740731,1.054765742824511,1.0736008453749486,1.0924359479253862,1.1112710504758239,1.1301061530262617,1.1489412555766993,1.167776358127137,1.1866114606775746,1.2054465632280125,1.22428166577845,1.2431167683288877,1.2619518708793256,1.2807869734297632,1.2996220759802009,1.3184571785306385,1.3372922810810763,1.356127383631514,1.3749624861819516,1.3937975887323895,1.412632691282827,1.4314677938332647,1.4503028963837024,1.4691379989341402,1.4879731014845778,1.5068082040350155,1.525643306585453,1.544478409135891,1.5633135116863286,1.5821486142367662,1.600983716787204,1.6198188193376417,1.6386539218880793,1.657489024438517,1.6763241269889548,1.6951592295393925,1.71399433208983,1.732829434640268,1.7516645371907056,1.7704996397411432,1.7893347422915808,1.8081698448420187,1.8270049473924563,1.845840049942894,1.8646751524933316,1.8835102550437695,1.902345357594207,1.9211804601446447,1.9400155626950826,1.9588506652455202,1.9776857677959578,1.9965208703463955,2.0153559728968333,2.034191075447271,2.0530261779977086,2.0718612805481462,2.090696383098584,2.109531485649022,2.1283665881994596,2.147201690749897,2.166036793300335,2.1848718958507725,2.20370699840121,2.2225421009516477,2.241377203502086,2.2602123060525234,2.279047408602961,2.2978825111533987,2.3167176137038363,2.335552716254274,2.3543878188047116,2.3732229213551492,2.3920580239055873,2.410893126456025,2.4297282290064626,2.4485633315569,2.467398434107338,2.4862335366577755,2.505068639208213,2.523903741758651,2.542738844309089,2.5615739468595264,2.580409049409964,2.5992441519604017,2.6180792545108393,2.636914357061277,2.655749459611715,2.6745845621621527,2.6934196647125903,2.712254767263028,2.7310898698134656,2.749924972363903,2.768760074914341,2.787595177464779,2.8064302800152165,2.825265382565654,2.844100485116092,2.8629355876665294,2.881770690216967,2.9006057927674047,2.919440895317843,2.9382759978682804,2.957111100418718,2.9759462029691557,2.9947813055195933,3.013616408070031,3.0324515106204686,3.051286613170906,3.0701217157213443,3.088956818271782,3.1077919208222196,3.126627023372657,3.145462125923095,3.1642972284735325,3.18313233102397,3.201967433574408,3.2208025361248453,3.2396376386752834,3.2584727412257206,3.2773078437761587,3.296142946326596,3.314978048877034,3.3338131514274716,3.3526482539779097,3.371483356528347,3.390318459078785,3.409153561629222,3.42798866417966,3.446823766730098,3.465658869280536,3.484493971830973,3.503329074381411,3.5221641769318484,3.5409992794822864,3.5598343820327236,3.5786694845831617,3.5975045871335993,3.6163396896840374,3.6351747922344746,3.6540098947849127,3.67284499733535,3.691680099885788,3.7105152024362256,3.729350304986663,3.748185407537101,3.767020510087539,3.785855612637976,3.804690715188414,3.8235258177388514,3.8423609202892894,3.861196022839727,3.880031125390165,3.8988662279406023,3.9177013304910404,3.9365364330414776,3.9553715355919157,3.974206638142353,3.993041740692791,4.011876843243228,4.030711945793667,4.049547048344104,4.068382150894542,4.0872172534449795,4.106052355995417,4.124887458545855,4.1437225610962924,4.16255766364673,4.181392766197168,4.200227868747605,4.219062971298044,4.237898073848481,4.256733176398919,4.275568278949356,4.294403381499794,4.313238484050231,4.33207358660067,4.350908689151107,4.369743791701545,4.3885788942519826,4.40741399680242,4.426249099352858,4.4450842019032955,4.463919304453733,4.482754407004172,4.501589509554608,4.520424612105047,4.539259714655484,4.558094817205922,4.576929919756359,4.595765022306797,4.614600124857235,4.633435227407673,4.65227032995811,4.671105432508548,4.689940535058986,4.708775637609423,4.727610740159861,4.7464458427102985,4.765280945260736,4.784116047811175,4.802951150361611,4.82178625291205,4.840621355462487,4.859456458012925,4.878291560563363,4.8971266631138,4.915961765664238,4.934796868214676,4.953631970765113,4.972467073315551,4.991302175865989,5.010137278416426,5.028972380966864,5.047807483517302,5.066642586067739,5.085477688618178,5.104312791168614,5.123147893719053,5.1419829962694905,5.160818098819928,5.179653201370366,5.198488303920803,5.217323406471241,5.236158509021679,5.254993611572116,5.273828714122554,5.292663816672992,5.31149891922343,5.330334021773867,5.349169124324305,5.368004226874742,5.386839329425181,5.405674431975618,5.424509534526056,5.4433446370764935,5.462179739626931,5.481014842177369,5.499849944727806,5.518685047278244,5.537520149828682,5.556355252379119,5.575190354929558,5.594025457479995,5.612860560030433,5.63169566258087,5.650530765131308,5.669365867681745,5.688200970232184,5.707036072782621,5.725871175333059,5.7447062778834965,5.763541380433934,5.782376482984372,5.801211585534809,5.820046688085247,5.838881790635686,5.857716893186122,5.876551995736561,5.895387098286998,5.914222200837436,5.933057303387873,5.951892405938311,5.970727508488749,5.989562611039187,6.008397713589624,6.027232816140062,6.0460679186904995,6.064903021240937,6.083738123791375,6.102573226341812,6.12140832889225,6.140243431442689,6.159078533993125,6.177913636543564,6.196748739094001,6.215583841644439,6.234418944194877,6.253254046745314,6.272089149295752,6.29092425184619,6.309759354396627,6.328594456947065,6.3474295594975025,6.36626466204794,6.385099764598378,6.403934867148816,6.422769969699253,6.441605072249691,6.460440174800128,6.479275277350567,6.4981103799010045,6.516945482451441,6.53578058500188,6.554615687552317,6.573450790102756,6.592285892653192,6.61112099520363,6.629956097754068,6.648791200304506,6.667626302854943,6.686461405405381,6.705296507955819,6.724131610506257,6.742966713056694,6.761801815607132,6.78063691815757,6.7994720207080075,6.818307123258444,6.837142225808883,6.85597732835932,6.874812430909759,6.893647533460196,6.912482636010633,6.931317738561072,6.950152841111509,6.968987943661946,6.987823046212384,7.006658148762822,7.02549325131326,7.044328353863697,7.063163456414135,7.081998558964573,7.100833661515011,7.119668764065447,7.138503866615886,7.157338969166323,7.176174071716762,7.195009174267199,7.213844276817636,7.232679379368075,7.2515144819185124,7.270349584468949,7.289184687019387,7.308019789569825,7.326854892120263,7.3456899946707,7.364525097221138,7.383360199771576,7.402195302322014,7.421030404872451,7.439865507422889,7.458700609973326,7.477535712523765,7.496370815074202,7.515205917624639,7.534041020175078,7.5528761227255155,7.571711225275952,7.590546327826391,7.609381430376828,7.628216532927267,7.647051635477703,7.665886738028141,7.684721840578579,7.703556943129017,7.722392045679454,7.741227148229892,7.76006225078033,7.778897353330768,7.797732455881205,7.816567558431642,7.835402660982081,7.8542377635325185,7.873072866082955,7.891907968633394,7.910743071183831,7.92957817373427,7.948413276284706,7.967248378835144,7.986083481385582,8.00491858393602,8.023753686486456,8.042588789036895,8.061423891587333,8.080258994137772,8.099094096688209,8.117929199238645,8.136764301789084,8.155599404339522,8.174434506889959,8.193269609440396,8.212104711990834,8.230939814541273,8.24977491709171,8.268610019642148,8.287445122192585,8.306280224743023,8.32511532729346,8.343950429843899,8.362785532394335,8.381620634944774,8.40045573749521,8.41929084004565,8.438125942596088,8.456961045146524,8.475796147696961,8.4946312502474,8.513466352797838,8.532301455348275,8.551136557898712,8.56997166044915,8.588806762999589,8.607641865550027,8.626476968100462,8.6453120706509,8.66414717320134,8.682982275751778,8.701817378302215,8.720652480852651,8.73948758340309,8.758322685953528,8.777157788503965,8.795992891054404,8.81482799360484,8.833663096155279,8.852498198705716,8.871333301256154,8.890168403806591,8.90900350635703,8.927838608907466,8.946673711457905,8.965508814008343,8.98434391655878,9.003179019109217,9.022014121659655,9.040849224210094,9.05968432676053,9.078519429310967,9.097354531861406,9.116189634411844,9.135024736962283,9.153859839512718,9.172694942063156,9.191530044613595,9.210365147164033,9.22920024971447,9.248035352264907,9.266870454815345,9.285705557365784,9.30454065991622,9.323375762466657,9.342210865017096,9.361045967567534,9.379881070117971,9.39871617266841,9.417551275218846,9.436386377769285,9.455221480319722,9.47405658287016,9.492891685420597,9.511726787971035,9.530561890521472,9.54939699307191,9.56823209562235,9.587067198172786,9.605902300723223,9.624737403273661,9.6435725058241,9.662407608374538,9.681242710924973,9.700077813475412,9.71891291602585,9.737748018576289,9.756583121126726,9.775418223677162,9.7942533262276,9.81308842877804,9.831923531328476,9.850758633878913,9.869593736429351,9.88842883897979,9.907263941530227,9.926099044080665,9.944934146631102,9.96376924918154,9.982604351731977,10.001439454282416,10.020274556832852,10.039109659383291,10.057944761933728,10.076779864484166,10.095614967034605,10.114450069585041,10.133285172135478,10.152120274685917,10.170955377236355,10.189790479786792,10.208625582337229,10.227460684887667,10.246295787438106,10.265130889988544,10.283965992538981,10.302801095089418,10.321636197639856,10.340471300190295,10.359306402740732,10.378141505291168,10.396976607841607,10.415811710392045,10.434646812942482,10.45348191549292,10.472317018043357,10.491152120593796,10.509987223144233,10.528822325694671,10.547657428245108,10.566492530795546,10.585327633345983,10.604162735896422,10.62299783844686,10.641832940997297,10.660668043547734,10.679503146098172,10.69833824864861,10.717173351199047,10.736008453749484,10.754843556299923,10.773678658850361,10.7925137614008,10.811348863951237,10.830183966501673,10.849019069052112,10.86785417160255,10.886689274152987,10.905524376703424,10.924359479253862,10.9431945818043,10.962029684354738,10.980864786905176,10.999699889455613,11.018534992006051,11.037370094556488,11.056205197106927,11.075040299657363,11.093875402207802,11.112710504758239,11.131545607308677,11.150380709859116,11.169215812409552,11.18805091495999,11.206886017510428,11.225721120060866,11.244556222611303,11.26339132516174,11.282226427712178,11.301061530262617,11.319896632813055,11.33873173536349,11.357566837913929,11.376401940464367,11.395237043014806,11.414072145565243,11.43290724811568,11.451742350666118,11.470577453216556,11.489412555766993,11.508247658317432,11.527082760867868,11.545917863418307,11.564752965968744,11.583588068519182,11.602423171069619,11.621258273620057,11.640093376170494,11.658928478720933,11.677763581271371,11.696598683821808,11.715433786372245,11.734268888922683,11.753103991473122,11.771939094023558,11.790774196573995,11.809609299124434,11.828444401674872,11.84727950422531,11.866114606775746,11.884949709326184,11.903784811876623,11.922619914427061,11.941455016977498,11.960290119527935,11.979125222078373,11.997960324628812,12.016795427179249,12.035630529729685,12.054465632280124,12.073300734830562,12.092135837380999,12.110970939931438,12.129806042481874,12.148641145032313,12.16747624758275,12.186311350133188,12.205146452683625,12.223981555234063,12.2428166577845,12.261651760334939,12.280486862885377,12.299321965435814,12.31815706798625,12.33699217053669,12.355827273087128,12.374662375637564,12.393497478188001,12.41233258073844,12.431167683288878,12.450002785839317,12.468837888389753,12.48767299094019,12.506508093490629,12.525343196041067,12.544178298591504,12.56301340114194,12.58184850369238,12.600683606242818,12.619518708793255,12.638353811343693,12.65718891389413,12.676024016444568,12.694859118995005,12.713694221545444,12.73252932409588,12.751364426646319,12.770199529196756,12.789034631747194,12.807869734297633,12.82670483684807,12.845539939398506,12.864375041948945],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237],"y":[0,0.02715272952057762,0.05430545904115524,0.08145818856173287,0.10861091808231048,0.13576364760288812,0.16291637712346574,0.19006910664404336,0.21722183616462096,0.2443745656851986,0.27152729520577623,0.29868002472635385,0.3258327542469315,0.3529854837675091,0.3801382132880867,0.40729094280866435,0.4344436723292419,0.4615964018498196,0.4887491313703972,0.5159018608909749,0.5430545904115525,0.57020731993213,0.5973600494527077,0.6245127789732853,0.651665508493863,0.6788182380144406,0.7059709675350182,0.7331236970555958,0.7602764265761734,0.7874291560967511,0.8145818856173287,0.8417346151379064,0.8688873446584838,0.8960400741790615,0.9231928036996392,0.9503455332202168,0.9774982627407944,1.004650992261372,1.0318037217819498,1.0589564513025271,1.086109180823105,1.1132619103436825,1.14041463986426,1.1675673693848378,1.1947200989054154,1.2218728284259932,1.2490255579465706,1.2761782874671481,1.303331016987726,1.3304837465083035,1.3576364760288813,1.3847892055494588,1.4119419350700364,1.439094664590614,1.4662473941111915,1.4934001236317693,1.520552853152347,1.5477055826729245,1.5748583121935023,1.6020110417140798,1.6291637712346574,1.6563165007552352,1.6834692302758127,1.71062195979639,1.7377746893169677,1.7649274188375454,1.792080148358123,1.8192328778787006,1.8463856073992784,1.873538336919856,1.9006910664404335,1.9278437959610113,1.9549965254815889,1.9821492550021667,2.009301984522744,2.036454714043322,2.0636074435638996,2.090760173084477,2.1179129026050543,2.145065632125632,2.17221836164621,2.199371091166787,2.226523820687365,2.2536765502079428,2.28082927972852,2.307982009249098,2.3351347387696757,2.3622874682902535,2.389440197810831,2.4165929273314086,2.4437456568519864,2.4708983863725633,2.498051115893141,2.525203845413719,2.5523565749342962,2.579509304454874,2.606662033975452,2.6338147634960296,2.660967493016607,2.6881202225371847,2.7152729520577625,2.74242568157834,2.7695784110989177,2.7967311406194955,2.823883870140073,2.85103659966065,2.878189329181228,2.9053420587018057,2.932494788222383,2.959647517742961,2.9868002472635387,3.013952976784116,3.041105706304694,3.0682584358252716,3.095411165345849,3.1225638948664267,3.1497166243870045,3.176869353907582,3.2040220834281596,3.231174812948737,3.2583275424693148,3.285480271989892,3.3126330015104704,3.3397857310310477,3.3669384605516255,3.394091190072203,3.42124391959278,3.448396649113358,3.4755493786339353,3.5027021081545135,3.529854837675091,3.5570075671956687,3.584160296716246,3.611313026236824,3.638465755757401,3.6656184852779794,3.6927712147985567,3.7199239443191345,3.747076673839712,3.77422940336029,3.801382132880867,3.8285348624014444,3.8556875919220226,3.8828403214426,3.9099930509631777,3.937145780483755,3.9642985100043333,3.99145123952491,4.018603969045488,4.045756698566065,4.072909428086644,4.100062157607221,4.127214887127799,4.1543676166483765,4.181520346168954,4.208673075689531,4.2358258052101085,4.262978534730687,4.290131264251264,4.317283993771842,4.34443672329242,4.371589452812997,4.398742182333574,4.425894911854153,4.45304764137473,4.480200370895308,4.5073531004158855,4.534505829936464,4.56165855945704,4.588811288977618,4.615964018498196,4.643116748018773,4.670269477539351,4.697422207059929,4.724574936580507,4.751727666101083,4.778880395621662,4.806033125142239,4.833185854662817,4.860338584183395,4.887491313703973,4.91464404322455,4.941796772745127,4.968949502265705,4.996102231786282,5.0232549613068604,5.050407690827438,5.077560420348016,5.1047131498685925,5.131865879389171,5.159018608909748,5.186171338430326,5.213324067950904,5.240476797471482,5.267629526992059,5.294782256512637,5.321934986033214,5.349087715553791,5.3762404450743695,5.403393174594947,5.430545904115525,5.457698633636102,5.48485136315668,5.512004092677257,5.539156822197835,5.566309551718413,5.593462281238991,5.620615010759568,5.647767740280146,5.674920469800723,5.7020731993213,5.7292259288418785,5.756378658362456,5.783531387883034,5.8106841174036115,5.837836846924189,5.864989576444766,5.892142305965344,5.919295035485922,5.9464477650065,5.973600494527077,6.000753224047655,6.027905953568232,6.05505868308881,6.082211412609388,6.109364142129965,6.136516871650543,6.1636696011711205,6.190822330691698,6.217975060212275,6.245127789732853,6.272280519253431,6.299433248774009,6.326585978294586,6.353738707815164,6.380891437335741,6.408044166856319,6.435196896376897],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"AI Rule 3","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377],"y":[0,0.022608107729241225,0.04521621545848245,0.06782432318772368,0.0904324309169649,0.11304053864620614,0.13564864637544735,0.1582567541046886,0.1808648618339298,0.20347296956317104,0.22608107729241228,0.24868918502165352,0.2712972927508947,0.29390540048013597,0.3165135082093772,0.3391216159386184,0.3617297236678596,0.3843378313971008,0.4069459391263421,0.42955404685558335,0.45216215458482456,0.47477026231406577,0.49737837004330704,0.5199864777725483,0.5425945855017894,0.5652026932310308,0.5878108009602719,0.6104189086895132,0.6330270164187544,0.6556351241479956,0.6782432318772368,0.700851339606478,0.7234594473357192,0.7460675550649605,0.7686756627942016,0.791283770523443,0.8138918782526842,0.8364999859819254,0.8591080937111667,0.8817162014404079,0.9043243091696491,0.9269324168988903,0.9495405246281315,0.9721486323573727,0.9947567400866141,1.0173648478158552,1.0399729555450965,1.0625810632743375,1.0851891710035788,1.10779727873282,1.1304053864620616,1.1530134941913026,1.1756216019205439,1.198229709649785,1.2208378173790264,1.2434459251082675,1.2660540328375087,1.2886621405667498,1.3112702482959913,1.3338783560252325,1.3564864637544736,1.379094571483715,1.401702679212956,1.4243107869421974,1.4469188946714384,1.46952700240068,1.492135110129921,1.5147432178591622,1.5373513255884033,1.5599594333176448,1.582567541046886,1.605175648776127,1.6277837565053683,1.6503918642346096,1.6729999719638509,1.695608079693092,1.7182161874223334,1.7408242951515744,1.7634324028808157,1.786040510610057,1.8086486183392982,1.8312567260685395,1.8538648337977806,1.8764729415270218,1.899081049256263,1.9216891569855044,1.9442972647147454,1.9669053724439869,1.9895134801732282,2.0121215879024694,2.0347296956317105,2.057337803360952,2.079945911090193,2.102554018819434,2.125162126548675,2.1477702342779166,2.1703783420071576,2.192986449736399,2.21559455746564,2.238202665194881,2.260810772924123,2.283418880653364,2.3060269883826052,2.3286350961118463,2.3512432038410878,2.373851311570329,2.39645941929957,2.419067527028811,2.441675634758053,2.464283742487294,2.486891850216535,2.5094999579457764,2.5321080656750174,2.5547161734042585,2.5773242811334995,2.5999323888627415,2.6225404965919825,2.6451486043212236,2.667756712050465,2.690364819779706,2.712972927508947,2.735581035238188,2.75818914296743,2.780797250696671,2.803405358425912,2.8260134661551533,2.8486215738843947,2.871229681613636,2.893837789342877,2.9164458970721183,2.93905400480136,2.961662112530601,2.984270220259842,3.0068783279890834,3.0294864357183244,3.0520945434475655,3.0747026511768065,3.0973107589060485,3.1199188666352895,3.1425269743645305,3.165135082093772,3.187743189823013,3.210351297552254,3.232959405281495,3.2555675130107367,3.278175620739978,3.300783728469219,3.3233918361984607,3.3459999439277017,3.3686080516569428,3.391216159386184,3.4138242671154253,3.436432374844667,3.459040482573908,3.481648590303149,3.5042566980323904,3.5268648057616314,3.5494729134908725,3.572081021220114,3.5946891289493554,3.6172972366785965,3.6399053444078375,3.662513452137079,3.68512155986632,3.707729667595561,3.730337775324802,3.7529458830540436,3.775553990783285,3.798162098512526,3.8207702062417677,3.8433783139710087,3.8659864217002498,3.888594529429491,3.9112026371587323,3.9338107448879738,3.956418852617215,3.9790269603464563,4.001635068075697,4.024243175804939,4.0468512835341794,4.069459391263421,4.0920674989926615,4.114675606721904,4.1372837144511445,4.159891822180386,4.1824999299096275,4.205108037638868,4.22771614536811,4.25032425309735,4.272932360826592,4.295540468555833,4.318148576285074,4.340756684014315,4.363364791743558,4.385972899472798,4.40858100720204,4.43118911493128,4.453797222660522,4.476405330389762,4.499013438119004,4.521621545848246,4.544229653577487,4.566837761306728,4.589445869035969,4.6120539767652105,4.634662084494451,4.6572701922236925,4.679878299952935,4.7024864076821755,4.725094515411417,4.747702623140658,4.770310730869899,4.79291883859914,4.815526946328381,4.838135054057622,4.860743161786864,4.883351269516106,4.905959377245346,4.928567484974588,4.951175592703828,4.97378370043307,4.99639180816231,5.018999915891553,5.041608023620794,5.064216131350035,5.086824239079276,5.109432346808517,5.1320404545377585,5.154648562266999,5.1772566699962415,5.199864777725483,5.2224728854547235,5.245080993183965,5.267689100913206,5.290297208642447,5.312905316371688,5.33551342410093,5.358121531830172,5.380729639559412,5.403337747288654,5.425945855017894,5.448553962747136,5.471162070476376,5.493770178205619,5.51637828593486,5.538986393664101,5.561594501393342,5.584202609122583,5.606810716851824,5.629418824581065,5.6520269323103065,5.674635040039548,5.6972431477687895,5.719851255498031,5.742459363227272,5.765067470956513,5.787675578685754,5.810283686414995,5.832891794144237,5.855499901873478,5.87810800960272,5.90071611733196,5.923324225061202,5.945932332790442,5.968540440519684,5.991148548248925,6.013756655978167,6.036364763707408,6.058972871436649,6.08158097916589,6.104189086895131,6.1267971946243724,6.149405302353613,6.172013410082855,6.194621517812097,6.2172296255413375,6.239837733270579,6.26244584099982,6.285053948729061,6.307662056458302,6.330270164187544,6.352878271916786,6.375486379646026,6.398094487375268,6.420702595104508,6.44331070283375,6.46591881056299,6.488526918292233,6.511135026021473,6.533743133750715,6.556351241479956,6.578959349209197,6.601567456938438,6.624175564667679,6.646783672396921,6.669391780126162,6.6919998878554035,6.714607995584645,6.7372161033138855,6.759824211043127,6.782432318772368,6.80504042650161,6.827648534230851,6.850256641960092,6.872864749689334,6.895472857418574,6.918080965147816,6.940689072877056,6.963297180606298,6.985905288335539,7.008513396064781,7.031121503794022,7.053729611523263,7.076337719252504,7.098945826981745,7.121553934710986,7.144162042440228,7.166770150169469,7.189378257898711,7.2119863656279515,7.234594473357193,7.257202581086434,7.279810688815675,7.3024187965449165,7.325026904274158,7.347635012003399,7.37024311973264,7.392851227461882,7.415459335191122,7.438067442920364,7.460675550649604,7.483283658378847,7.505891766108087,7.528499873837329,7.55110798156657,7.573716089295811,7.596324197025052,7.618932304754293,7.641540412483535,7.664148520212776,7.686756627942017,7.709364735671259,7.7319728434004995,7.754580951129741,7.777189058858982,7.799797166588224,7.822405274317465,7.845013382046706,7.8676214897759476,7.890229597505188,7.91283770523443,7.93544581296367,7.958053920692913,7.980662028422153,8.003270136151395,8.025878243880635,8.048486351609878,8.071094459339118,8.093702567068359,8.116310674797601,8.138918782526842,8.161526890256082,8.184134997985323,8.206743105714565,8.229351213443808,8.251959321173048,8.274567428902289,8.29717553663153,8.319783644360772,8.342391752090013,8.364999859819255,8.387607967548496,8.410216075277736,8.432824183006979,8.45543229073622,8.47804039846546,8.5006485061947,8.523256613923943],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">24.58%</td>
                <td>64.9%</td>
                <td>936</td>
                <td>1.11</td>
                <td>0.00</td>
                <td>13.09%</td>
                <td>59.3</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">16.41%</td>
                <td>65.2%</td>
                <td>689</td>
                <td>1.10</td>
                <td>0.00</td>
                <td>7.25%</td>
                <td>56.1</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Ext Rule 6: Fibonacci Support Confluence</td>
                <td>UNKNOWN</td>
                <td class="positive">12.86%</td>
                <td>65.2%</td>
                <td>683</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>15.30%</td>
                <td>54.7</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">6.44%</td>
                <td>66.2%</td>
                <td>237</td>
                <td>1.11</td>
                <td>0.00</td>
                <td>7.31%</td>
                <td>52.4</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>AI Rule 3: Smart Money Flow Divergence</td>
                <td>AI_GENERATED</td>
                <td class="positive">8.52%</td>
                <td>63.4%</td>
                <td>377</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>7.03%</td>
                <td>52.4</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>AI Rule 8: Momentum Divergence Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">4.26%</td>
                <td>66.7%</td>
                <td>114</td>
                <td>1.16</td>
                <td>0.00</td>
                <td>5.92%</td>
                <td>51.7</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">6.04%</td>
                <td>64.6%</td>
                <td>99</td>
                <td>1.27</td>
                <td>0.00</td>
                <td>4.54%</td>
                <td>51.5</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Ext Rule 5: ATR Volatility Expansion</td>
                <td>UNKNOWN</td>
                <td class="positive">2.07%</td>
                <td>59.5%</td>
                <td>111</td>
                <td>1.07</td>
                <td>0.00</td>
                <td>3.60%</td>
                <td>48.7</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Professional Rule 10: CCI Reversal Enhanced</td>
                <td>UNKNOWN</td>
                <td class="positive">2.53%</td>
                <td>68.5%</td>
                <td>54</td>
                <td>1.22</td>
                <td>0.00</td>
                <td>2.67%</td>
                <td>37.8</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">4.01%</td>
                <td>69.7%</td>
                <td>33</td>
                <td>1.72</td>
                <td>0.00</td>
                <td>1.72%</td>
                <td>32.4</td>
            </tr>
            
            <tr>
                <td>11</td>
                <td>Advanced Rule 7: DMI ADX Filter</td>
                <td>UNKNOWN</td>
                <td class="positive">1.04%</td>
                <td>63.6%</td>
                <td>11</td>
                <td>1.42</td>
                <td>0.00</td>
                <td>0.79%</td>
                <td>22.8</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 525,301<br>
            • Backtest Range: 300 to 525,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
