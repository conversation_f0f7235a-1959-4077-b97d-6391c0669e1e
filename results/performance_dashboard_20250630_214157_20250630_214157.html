
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 16 Top-Performing Buy Rules</p>
        <p>Generated: 2025-06-30 21:41:57</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">16</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">14.0%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">3.0%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">8.0%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">67.6%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">1,438</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["8.0%","6.1%","6.3%","1.3%","1.7%","1.8%","1.1%","3.7%","4.1%","2.1%"],"textposition":"auto","x":["Research Rule 6: Institutional Order Flow","AI Rule 6: Market Structure Shift","Research Rule 3: Momentum Ignition","Advanced Rule 1: TSI Crossover","AI Rule 1: Multi-Timeframe Momentum","Volatility Rule 3: Volatility Compression Release","Rule 18: TRIX Signal","Professional Rule 11: Awesome Oscillator Zero Cross","Rule 16: CCI Oversold","New Rule 1: PPO Crossover"],"y":[7.987742556288009,6.05439919621003,6.28229263352498,1.3329160340100972,1.6782245942570118,1.8123950709033017,1.1022231799411966,3.6916275724797707,4.050432449325243,2.0900695436211425],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Research Rule 6: Institutional Order Flow","AI Rule 6: Market Structure Shift","Research Rule 3: Momentum Ignition","Advanced Rule 1: TSI Crossover","AI Rule 1: Multi-Timeframe Momentum","Volatility Rule 3: Volatility Compression Release","Rule 18: TRIX Signal","Professional Rule 11: Awesome Oscillator Zero Cross","Rule 16: CCI Oversold","New Rule 1: PPO Crossover"],"y":[177,90,203,67,152,42,36,25,18,25],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Research Rule 6: Institutional Order Flow","AI Rule 6: Market Structure Shift","Research Rule 3: Momentum Ignition","Advanced Rule 1: TSI Crossover","AI Rule 1: Multi-Timeframe Momentum","Volatility Rule 3: Volatility Compression Release","Rule 18: TRIX Signal","Professional Rule 11: Awesome Oscillator Zero Cross","Rule 16: CCI Oversold","New Rule 1: PPO Crossover"],"y":[100,47,119,39,101,23,21,10,6,14],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[7.987742556288009,6.05439919621003,6.28229263352498,1.3329160340100972,1.6782245942570118,1.8123950709033017,1.1022231799411966,3.6916275724797707,4.050432449325243,2.0900695436211425,2.033428027918388,2.1990423412984748,2.1912036043268417,2.148268448416835,1.3157096335946699,1.389954002961931],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Research Rule 6","AI Rule 6","Research Rule 3","Advanced Rule 1","AI Rule 1","Volatility Rule 3","Rule 18","Professional Rule 11","Rule 16","New Rule 1","Research Rule 5","Price Action Rule 3","Rule 2","Rule 7","Rule 6","GitHub Rule 4"],"textposition":"top center","x":[5.656326748442265,3.4686820955844557,7.474998321384692,5.138206967836795,8.59297867517012,4.2985311287031065,4.74527614131203,2.3530033016939718,1.0857685963372572,2.623569382002284,3.303429589970387,2.7031347203516813,1.0123822004856284,1.0191929723585644,1.0689055352088832,2.2167203308468353],"y":[7.987742556288009,6.05439919621003,6.28229263352498,1.3329160340100972,1.6782245942570118,1.8123950709033017,1.1022231799411966,3.6916275724797707,4.050432449325243,2.0900695436211425,2.033428027918388,2.1990423412984748,2.1912036043268417,2.148268448416835,1.3157096335946699,1.389954002961931],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22"]},"text":["3.2%","3.9%","2.2%"],"textposition":"auto","x":["UNKNOWN","AI_GENERATED","ORIGINAL"],"y":[3.202163087000678,3.8663118952335207,2.1615674631209574],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["277","137","322","106","253","65","57","35","24","39"],"textposition":"auto","x":["Research Rule 6: Institutional Order Flow","AI Rule 6: Market Structure Shift","Research Rule 3: Momentum Ignition","Advanced Rule 1: TSI Crossover","AI Rule 1: Multi-Timeframe Momentum","Volatility Rule 3: Volatility Compression Release","Rule 18: TRIX Signal","Professional Rule 11: Awesome Oscillator Zero Cross","Rule 16: CCI Oversold","New Rule 1: PPO Crossover"],"y":[277,137,322,106,253,65,57,35,24,39],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Research Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277],"y":[0,0.028836615726671514,0.05767323145334303,0.08650984718001453,0.11534646290668606,0.14418307863335758,0.17301969436002906,0.20185631008670057,0.2306929258133721,0.2595295415400436,0.28836615726671516,0.3172027729933867,0.3460393887200581,0.3748760044467297,0.40371262017340115,0.4325492359000727,0.4613858516267442,0.49022246735341574,0.5190590830800872,0.5478956988067588,0.5767323145334303,0.6055689302601017,0.6344055459867733,0.6632421617134449,0.6920787774401163,0.7209153931667879,0.7497520088934594,0.7785886246201309,0.8074252403468023,0.8362618560734739,0.8650984718001454,0.8939350875268168,0.9227717032534885,0.95160831898016,0.9804449347068315,1.0092815504335029,1.0381181661601744,1.0669547818868461,1.0957913976135176,1.1246280133401891,1.1534646290668606,1.182301244793532,1.2111378605202034,1.2399744762468752,1.2688110919735467,1.2976477077002182,1.3264843234268897,1.3553209391535612,1.3841575548802325,1.412994170606904,1.4418307863335758,1.4706674020602473,1.4995040177869188,1.5283406335135903,1.5571772492402618,1.586013864966933,1.6148504806936046,1.6436870964202763,1.6725237121469478,1.7013603278736193,1.7301969436002909,1.7590335593269624,1.7878701750536337,1.8167067907803054,1.845543406506977,1.8743800222336484,1.90321663796032,1.9320532536869914,1.960889869413663,1.9897264851403345,2.0185631008670057,2.0473997165936773,2.0762363323203488,2.1050729480470207,2.1339095637736922,2.1627461795003637,2.1915827952270353,2.2204194109537068,2.2492560266803783,2.27809264240705,2.3069292581337213,2.3357658738603924,2.364602489587064,2.3934391053137354,2.422275721040407,2.4511123367670784,2.4799489524937504,2.508785568220422,2.5376221839470934,2.566458799673765,2.5952954154004364,2.624132031127108,2.6529686468537794,2.681805262580451,2.7106418783071224,2.7394784940337935,2.768315109760465,2.7971517254871365,2.825988341213808,2.85482495694048,2.8836615726671515,2.912498188393823,2.9413348041204945,2.970171419847166,2.9990080355738375,3.027844651300509,3.0566812670271806,3.085517882753852,3.1143544984805236,3.143191114207195,3.172027729933866,3.2008643456605377,3.229700961387209,3.258537577113881,3.2873741928405527,3.316210808567224,3.3450474242938957,3.373884040020567,3.4027206557472387,3.43155727147391,3.4603938872005817,3.4892305029272532,3.5180671186539247,3.5469037343805963,3.5757403501072673,3.604576965833939,3.633413581560611,3.6622501972872823,3.691086813013954,3.7199234287406253,3.748760044467297,3.7775966601939683,3.80643327592064,3.8352698916473114,3.864106507373983,3.8929431231006544,3.921779738827326,3.9506163545539974,3.979452970280669,4.008289586007341,4.0371262017340115,4.065962817460684,4.0947994331873545,4.123636048914027,4.1524726646406975,4.18130928036737,4.210145896094041,4.238982511820712,4.2678191275473845,4.296655743274055,4.3254923590007275,4.354328974727398,4.3831655904540705,4.412002206180741,4.4408388219074135,4.469675437634084,4.4985120533607565,4.527348669087427,4.5561852848141,4.585021900540771,4.613858516267443,4.642695131994114,4.671531747720785,4.700368363447457,4.729204979174128,4.7580415949008,4.786878210627471,4.815714826354143,4.844551442080814,4.873388057807486,4.902224673534157,4.931061289260829,4.959897904987501,4.988734520714172,5.017571136440844,5.046407752167514,5.075244367894187,5.104080983620857,5.13291759934753,5.1617542150742,5.190590830800873,5.219427446527543,5.248264062254216,5.2771006779808864,5.305937293707559,5.33477390943423,5.363610525160902,5.392447140887573,5.421283756614245,5.450120372340916,5.478956988067587,5.507793603794259,5.53663021952093,5.565466835247602,5.594303450974273,5.6231400667009455,5.651976682427616,5.6808132981542885,5.70964991388096,5.7384865296076315,5.767323145334303,5.7961597610609745,5.824996376787646,5.8538329925143175,5.882669608240989,5.91150622396766,5.940342839694332,5.969179455421003,5.998016071147675,6.026852686874346,6.055689302601018,6.084525918327689,6.113362534054361,6.142199149781033,6.171035765507704,6.199872381234376,6.228708996961047,6.257545612687719,6.28638222841439,6.315218844141062,6.344055459867732,6.372892075594405,6.401728691321075,6.430565307047748,6.459401922774418,6.488238538501091,6.517075154227762,6.545911769954434,6.574748385681105,6.603585001407777,6.632421617134448,6.66125823286112,6.690094848587791,6.718931464314463,6.747768080041134,6.776604695767805,6.805441311494477,6.834277927221148,6.86311454294782,6.891951158674492,6.920787774401163,6.949624390127835,6.9784610058545065,7.007297621581178,7.0361342373078495,7.064970853034521,7.0938074687611925,7.122644084487864,7.151480700214535,7.180317315941207,7.209153931667878,7.23799054739455,7.266827163121222,7.295663778847893,7.324500394574565,7.353337010301236,7.382173626027908,7.411010241754579,7.439846857481251,7.468683473207922,7.497520088934594,7.526356704661265,7.555193320387937,7.584029936114607,7.61286655184128,7.641703167567951,7.670539783294623,7.699376399021294,7.728213014747966,7.757049630474637,7.785886246201309,7.81472286192798,7.843559477654652,7.872396093381323,7.901232709107995,7.930069324834666,7.958905940561338,7.987742556288009],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"AI Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137],"y":[0,0.04419269486284693,0.08838538972569386,0.1325780845885408,0.17677077945138772,0.22096347431423463,0.2651561691770816,0.3093488640399285,0.35354155890277544,0.3977342537656224,0.44192694862846926,0.48611964349131626,0.5303123383541632,0.5745050332170101,0.618697728079857,0.6628904229427041,0.7070831178055509,0.7512758126683978,0.7954685075312448,0.8396612023940917,0.8838538972569385,0.9280465921197856,0.9722392869826325,1.0164319818454794,1.0606246767083265,1.1048173715711733,1.1490100664340201,1.1932027612968672,1.237395456159714,1.281588151022561,1.3257808458854081,1.369973540748255,1.4141662356111018,1.4583589304739488,1.5025516253367956,1.5467443201996427,1.5909370150624895,1.6351297099253366,1.6793224047881834,1.7235150996510302,1.767707794513877,1.8119004893767243,1.8560931842395711,1.9002858791024182,1.944478573965265,1.9886712688281118,2.032863963690959,2.077056658553806,2.121249353416653,2.1654420482794996,2.2096347431423466,2.2538274380051933,2.2980201328680403,2.3422128277308873,2.3864055225937344,2.4305982174565814,2.474790912319428,2.518983607182275,2.563176302045122,2.6073689969079688,2.6515616917708162,2.695754386633663,2.73994708149651,2.7841397763593565,2.8283324712222035,2.8725251660850506,2.9167178609478976,2.9609105558107447,3.0051032506735913,3.049295945536439,3.0934886403992854,3.1376813352621324,3.181874030124979,3.226066724987826,3.270259419850673,3.3144521147135197,3.358644809576367,3.4028375044392134,3.4470301993020604,3.4912228941649075,3.535415589027754,3.5796082838906016,3.6238009787534486,3.6679936736162957,3.7121863684791423,3.7563790633419893,3.8005717582048364,3.844764453067683,3.88895714793053,3.9331498427933766,3.9773425376562237,4.021535232519071,4.065727927381918,4.109920622244764,4.154113317107612,4.198306011970459,4.242498706833306,4.286691401696152,4.330884096558999,4.375076791421846,4.419269486284693,4.46346218114754,4.5076548760103865,4.5518475708732336,4.596040265736081,4.640232960598928,4.684425655461775,4.728618350324622,4.772811045187469,4.817003740050316,4.861196434913163,4.905389129776009,4.949581824638856,4.993774519501703,5.03796721436455,5.082159909227397,5.126352604090244,5.1705452989530905,5.2147379938159375,5.258930688678785,5.3031233835416325,5.347316078404479,5.391508773267326,5.435701468130173,5.47989416299302,5.524086857855867,5.568279552718713,5.61247224758156,5.656664942444407,5.700857637307254,5.745050332170101,5.789243027032948,5.833435721895795,5.877628416758642,5.921821111621489,5.9660138064843355,6.010206501347183,6.05439919621003],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Research Rule 3","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322],"y":[0,0.019510225569953354,0.03902045113990671,0.05853067670986006,0.07804090227981342,0.09755112784976677,0.11706135341972013,0.13657157898967348,0.15608180455962684,0.1755920301295802,0.19510225569953354,0.2146124812694869,0.23412270683944025,0.2536329324093936,0.27314315797934696,0.2926533835493003,0.31216360911925367,0.331673834689207,0.3511840602591604,0.37069428582911373,0.3902045113990671,0.40971473696902044,0.4292249625389738,0.4487351881089271,0.4682454136788805,0.4877556392488338,0.5072658648187872,0.5267760903887406,0.5462863159586939,0.5657965415286473,0.5853067670986006,0.6048169926685539,0.6243272182385073,0.6438374438084606,0.663347669378414,0.6828578949483673,0.7023681205183208,0.7218783460882741,0.7413885716582275,0.7608987972281809,0.7804090227981342,0.7999192483680875,0.8194294739380409,0.8389396995079942,0.8584499250779476,0.8779601506479009,0.8974703762178542,0.9169806017878076,0.936490827357761,0.9560010529277144,0.9755112784976676,0.995021504067621,1.0145317296375744,1.0340419552075277,1.0535521807774813,1.0730624063474343,1.0925726319173878,1.1120828574873411,1.1315930830572947,1.151103308627248,1.1706135341972013,1.1901237597671546,1.2096339853371079,1.2291442109070614,1.2486544364770147,1.268164662046968,1.2876748876169213,1.3071851131868748,1.326695338756828,1.3462055643267814,1.3657157898967347,1.3852260154666882,1.4047362410366415,1.4242464666065948,1.4437566921765481,1.4632669177465016,1.482777143316455,1.5022873688864082,1.5217975944563618,1.5413078200263148,1.5608180455962684,1.5803282711662214,1.599838496736175,1.6193487223061283,1.6388589478760818,1.658369173446035,1.6778793990159884,1.697389624585942,1.7168998501558952,1.7364100757258487,1.7559203012958018,1.775430526865755,1.7949407524357084,1.814450978005662,1.8339612035756152,1.8534714291455687,1.872981654715522,1.8924918802854753,1.9120021058554288,1.9315123314253821,1.9510225569953352,1.9705327825652887,1.990043008135242,2.0095532337051956,2.029063459275149,2.048573684845102,2.0680839104150555,2.087594135985009,2.1071043615549625,2.1266145871249154,2.1461248126948687,2.1656350382648224,2.1851452638347757,2.204655489404729,2.2241657149746823,2.2436759405446356,2.2631861661145893,2.2826963916845426,2.302206617254496,2.321716842824449,2.3412270683944025,2.360737293964356,2.380247519534309,2.3997577451042624,2.4192679706742157,2.4387781962441695,2.4582884218141228,2.477798647384076,2.4973088729540294,2.5168190985239827,2.536329324093936,2.5558395496638893,2.5753497752338426,2.5948600008037963,2.6143702263737496,2.633880451943703,2.653390677513656,2.6729009030836095,2.692411128653563,2.711921354223516,2.7314315797934694,2.7509418053634227,2.7704520309333764,2.7899622565033297,2.809472482073283,2.8289827076432363,2.8484929332131896,2.868003158783143,2.8875133843530962,2.9070236099230495,2.9265338354930033,2.9460440610629566,2.96555428663291,2.985064512202863,3.0045747377728165,3.02408496334277,3.0435951889127235,3.0631054144826764,3.0826156400526297,3.1021258656225834,3.1216360911925367,3.14114631676249,3.160656542332443,3.1801667679023966,3.19967699347235,3.2191872190423036,3.2386974446122565,3.2582076701822102,3.2777178957521635,3.2972281213221173,3.31673834689207,3.3362485724620234,3.3557587980319767,3.37526902360193,3.394779249171884,3.4142894747418366,3.4337997003117904,3.4533099258817437,3.4728201514516974,3.4923303770216503,3.5118406025916036,3.5313508281615573,3.55086105373151,3.570371279301464,3.5898815048714168,3.6093917304413705,3.628901956011324,3.6484121815812776,3.6679224071512304,3.687432632721184,3.7069428582911375,3.7264530838610903,3.745963309431044,3.765473535000997,3.7849837605709507,3.804493986140904,3.8240042117108577,3.8435144372808105,3.8630246628507643,3.8825348884207176,3.9020451139906704,3.921555339560624,3.9410655651305775,3.9605757907005312,3.980086016270484,3.999596241840438,4.019106467410391,4.038616692980344,4.058126918550298,4.077637144120251,4.097147369690204,4.116657595260158,4.136167820830111,4.155678046400064,4.175188271970018,4.194698497539971,4.214208723109925,4.233718948679877,4.253229174249831,4.272739399819785,4.292249625389737,4.3117598509596915,4.331270076529645,4.350780302099598,4.370290527669551,4.389800753239505,4.409310978809458,4.428821204379411,4.448331429949365,4.467841655519318,4.487351881089271,4.5068621066592245,4.526372332229179,4.545882557799131,4.565392783369085,4.584903008939039,4.604413234508992,4.623923460078945,4.643433685648898,4.662943911218852,4.682454136788805,4.701964362358758,4.721474587928712,4.740984813498665,4.760495039068618,4.7800052646385724,4.799515490208525,4.819025715778478,4.8385359413484315,4.858046166918385,4.877556392488339,4.897066618058291,4.9165768436282455,4.936087069198199,4.955597294768152,4.975107520338105,4.994617745908059,5.014127971478012,5.033638197047965,5.053148422617919,5.072658648187872,5.092168873757825,5.1116790993277785,5.131189324897733,5.150699550467685,5.170209776037639,5.189720001607593,5.209230227177545,5.228740452747499,5.248250678317452,5.267760903887406,5.287271129457359,5.306781355027312,5.326291580597266,5.345801806167219,5.365312031737172,5.384822257307126,5.404332482877079,5.423842708447032,5.443352934016986,5.462863159586939,5.482373385156893,5.501883610726845,5.5213938362968,5.540904061866753,5.560414287436705,5.5799245130066595,5.599434738576613,5.618944964146566,5.638455189716519,5.657965415286473,5.677475640856426,5.696985866426379,5.716496091996333,5.736006317566286,5.755516543136239,5.7750267687061925,5.794536994276147,5.814047219846099,5.833557445416053,5.8530676709860066,5.87257789655596,5.892088122125913,5.9115983476958665,5.93110857326582,5.950618798835773,5.970129024405726,5.98963924997568,6.009149475545633,6.028659701115586,6.04816992668554,6.067680152255493,6.087190377825447,6.106700603395399,6.126210828965353,6.145721054535307,6.165231280105259,6.1847415056752135,6.204251731245167,6.22376195681512,6.243272182385073,6.262782407955027,6.28229263352498],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Advanced Rule 1","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106],"y":[0,0.012574679566132991,0.025149359132265982,0.03772403869839898,0.050298718264531965,0.06287339783066496,0.07544807739679796,0.08802275696293095,0.10059743652906393,0.11317211609519692,0.12574679566132993,0.1383214752274629,0.15089615479359592,0.1634708343597289,0.1760455139258619,0.18862019349199488,0.20119487305812786,0.21376955262426087,0.22634423219039385,0.23891891175652682,0.25149359132265986,0.26406827088879287,0.2766429504549258,0.2892176300210588,0.30179230958719183,0.3143669891533248,0.3269416687194578,0.3395163482855908,0.3520910278517238,0.36466570741785675,0.37724038698398976,0.38981506655012277,0.4023897461162557,0.4149644256823887,0.42753910524852173,0.4401137848146547,0.4526884643807877,0.4652631439469207,0.47783782351305365,0.49041250307918666,0.5029871826453197,0.5155618622114527,0.5281365417775857,0.5407112213437187,0.5532859009098516,0.5658605804759846,0.5784352600421176,0.5910099396082507,0.6035846191743837,0.6161592987405167,0.6287339783066496,0.6413086578727826,0.6538833374389156,0.6664580170050486,0.6790326965711816,0.6916073761373146,0.7041820557034476,0.7167567352695805,0.7293314148357135,0.7419060944018465,0.7544807739679795,0.7670554535341125,0.7796301331002455,0.7922048126663784,0.8047794922325114,0.8173541717986444,0.8299288513647775,0.8425035309309105,0.8550782104970435,0.8676528900631764,0.8802275696293094,0.8928022491954424,0.9053769287615754,0.9179516083277084,0.9305262878938414,0.9431009674599744,0.9556756470261073,0.9682503265922403,0.9808250061583733,0.9933996857245063,1.0059743652906394,1.0185490448567724,1.0311237244229055,1.0436984039890385,1.0562730835551715,1.0688477631213045,1.0814224426874375,1.0939971222535703,1.1065718018197033,1.1191464813858363,1.1317211609519693,1.1442958405181023,1.1568705200842353,1.1694451996503683,1.1820198792165013,1.1945945587826343,1.2071692383487673,1.2197439179149003,1.2323185974810333,1.2448932770471663,1.2574679566132991,1.2700426361794321,1.2826173157455651,1.2951919953116982,1.3077666748778312,1.3203413544439642,1.3329160340100972],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"AI Rule 1","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253],"y":[0,0.00663329879152969,0.01326659758305938,0.019899896374589072,0.02653319516611876,0.03316649395764845,0.039799792749178144,0.046433091540707835,0.05306639033223752,0.05969968912376722,0.0663329879152969,0.0729662867068266,0.07959958549835629,0.08623288428988599,0.09286618308141567,0.09949948187294536,0.10613278066447504,0.11276607945600474,0.11939937824753444,0.12603267703906412,0.1326659758305938,0.13929927462212352,0.1459325734136532,0.1525658722051829,0.15919917099671257,0.16583246978824226,0.17246576857977197,0.17909906737130163,0.18573236616283134,0.19236566495436103,0.1989989637458907,0.20563226253742042,0.21226556132895008,0.2188988601204798,0.22553215891200948,0.2321654577035392,0.23879875649506888,0.24543205528659853,0.25206535407812825,0.25869865286965793,0.2653319516611876,0.27196525045271736,0.27859854924424704,0.28523184803577667,0.2918651468273064,0.2984984456188361,0.3051317444103658,0.31176504320189546,0.31839834199342515,0.32503164078495483,0.3316649395764845,0.33829823836801426,0.34493153715954394,0.35156483595107363,0.35819813474260326,0.364831433534133,0.3714647323256627,0.37809803111719237,0.38473132990872205,0.3913646287002518,0.3979979274917814,0.4046312262833111,0.41126452507484085,0.41789782386637053,0.42453112265790016,0.4311644214494299,0.4377977202409596,0.4444310190324893,0.45106431782401896,0.45769761661554864,0.4643309154070784,0.470964214198608,0.47759751299013775,0.48423081178166744,0.49086411057319707,0.4974974093647268,0.5041307081562565,0.5107640069477862,0.5173973057393159,0.5240306045308455,0.5306639033223752,0.537297202113905,0.5439305009054347,0.5505637996969643,0.5571970984884941,0.5638303972800237,0.5704636960715533,0.5770969948630831,0.5837302936546128,0.5903635924461426,0.5969968912376722,0.6036301900292018,0.6102634888207316,0.6168967876122612,0.6235300864037909,0.6301633851953207,0.6367966839868503,0.64342998277838,0.6500632815699097,0.6566965803614394,0.663329879152969,0.6699631779444988,0.6765964767360285,0.6832297755275581,0.6898630743190879,0.6964963731106175,0.7031296719021473,0.7097629706936769,0.7163962694852065,0.7230295682767363,0.729662867068266,0.7362961658597957,0.7429294646513254,0.749562763442855,0.7561960622343847,0.7628293610259144,0.7694626598174441,0.7760959586089738,0.7827292574005036,0.7893625561920332,0.7959958549835628,0.8026291537750926,0.8092624525666222,0.815895751358152,0.8225290501496817,0.8291623489412113,0.8357956477327411,0.8424289465242707,0.8490622453158003,0.8556955441073302,0.8623288428988598,0.8689621416903895,0.8755954404819192,0.8822287392734488,0.8888620380649787,0.8954953368565083,0.9021286356480379,0.9087619344395677,0.9153952332310973,0.9220285320226271,0.9286618308141568,0.9352951296056864,0.941928428397216,0.9485617271887457,0.9551950259802755,0.9618283247718052,0.9684616235633349,0.9750949223548645,0.9817282211463941,0.988361519937924,0.9949948187294536,1.0016281175209834,1.008261416312513,1.0148947151040426,1.0215280138955725,1.028161312687102,1.0347946114786317,1.0414279102701613,1.048061209061691,1.0546945078532208,1.0613278066447505,1.06796110543628,1.07459440422781,1.0812277030193398,1.0878610018108694,1.094494300602399,1.1011275993939287,1.1077608981854583,1.1143941969769882,1.1210274957685178,1.1276607945600474,1.134294093351577,1.1409273921431067,1.1475606909346365,1.1541939897261662,1.1608272885176958,1.1674605873092256,1.1740938861007553,1.1807271848922851,1.1873604836838147,1.1939937824753444,1.200627081266874,1.2072603800584036,1.2138936788499335,1.2205269776414631,1.2271602764329927,1.2337935752245224,1.240426874016052,1.2470601728075819,1.2536934715991115,1.2603267703906413,1.266960069182171,1.2735933679737006,1.2802266667652304,1.28685996555676,1.2934932643482897,1.3001265631398193,1.306759861931349,1.3133931607228788,1.3200264595144084,1.326659758305938,1.3332930570974677,1.3399263558889976,1.3465596546805272,1.353192953472057,1.3598262522635867,1.3664595510551163,1.3730928498466461,1.3797261486381758,1.3863594474297054,1.392992746221235,1.3996260450127647,1.4062593438042945,1.4128926425958241,1.4195259413873538,1.4261592401788834,1.432792538970413,1.4394258377619429,1.4460591365534725,1.4526924353450024,1.459325734136532,1.4659590329280616,1.4725923317195915,1.479225630511121,1.4858589293026507,1.4924922280941804,1.49912552688571,1.5057588256772398,1.5123921244687695,1.519025423260299,1.5256587220518287,1.5322920208433586,1.5389253196348882,1.545558618426418,1.5521919172179477,1.5588252160094773,1.5654585148010072,1.5720918135925368,1.5787251123840664,1.585358411175596,1.5919917099671257,1.5986250087586555,1.6052583075501852,1.6118916063417148,1.6185249051332444,1.625158203924774,1.631791502716304,1.6384248015078338,1.6450581002993634,1.651691399090893,1.6583246978824226,1.6649579966739525,1.6715912954654821,1.6782245942570118],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Research Rule 6: Institutional Order Flow</td>
                <td>UNKNOWN</td>
                <td class="positive">7.99%</td>
                <td>63.9%</td>
                <td>277</td>
                <td>1.11</td>
                <td>0.00</td>
                <td>5.66%</td>
                <td>52.4</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>AI Rule 6: Market Structure Shift</td>
                <td>AI_GENERATED</td>
                <td class="positive">6.05%</td>
                <td>65.7%</td>
                <td>137</td>
                <td>1.18</td>
                <td>0.00</td>
                <td>3.47%</td>
                <td>52.1</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Research Rule 3: Momentum Ignition</td>
                <td>UNKNOWN</td>
                <td class="positive">6.28%</td>
                <td>63.0%</td>
                <td>322</td>
                <td>1.07</td>
                <td>0.00</td>
                <td>7.47%</td>
                <td>51.4</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Advanced Rule 1: TSI Crossover</td>
                <td>UNKNOWN</td>
                <td class="positive">1.33%</td>
                <td>64.2%</td>
                <td>106</td>
                <td>1.05</td>
                <td>0.00</td>
                <td>5.14%</td>
                <td>49.8</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>AI Rule 1: Multi-Timeframe Momentum</td>
                <td>AI_GENERATED</td>
                <td class="positive">1.68%</td>
                <td>60.5%</td>
                <td>253</td>
                <td>1.02</td>
                <td>0.00</td>
                <td>8.59%</td>
                <td>48.8</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Volatility Rule 3: Volatility Compression Release</td>
                <td>UNKNOWN</td>
                <td class="positive">1.81%</td>
                <td>64.6%</td>
                <td>65</td>
                <td>1.11</td>
                <td>0.00</td>
                <td>4.30%</td>
                <td>39.6</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Rule 18: TRIX Signal</td>
                <td>ORIGINAL</td>
                <td class="positive">1.10%</td>
                <td>63.2%</td>
                <td>57</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>4.75%</td>
                <td>36.5</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Professional Rule 11: Awesome Oscillator Zero Cross</td>
                <td>UNKNOWN</td>
                <td class="positive">3.69%</td>
                <td>71.4%</td>
                <td>35</td>
                <td>1.64</td>
                <td>0.00</td>
                <td>2.35%</td>
                <td>33.4</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Rule 16: CCI Oversold</td>
                <td>ORIGINAL</td>
                <td class="positive">4.05%</td>
                <td>79.2%</td>
                <td>24</td>
                <td>2.06</td>
                <td>0.00</td>
                <td>1.09%</td>
                <td>32.6</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>New Rule 1: PPO Crossover</td>
                <td>UNKNOWN</td>
                <td class="positive">2.09%</td>
                <td>64.1%</td>
                <td>39</td>
                <td>1.25</td>
                <td>0.00</td>
                <td>2.62%</td>
                <td>31.8</td>
            </tr>
            
            <tr>
                <td>11</td>
                <td>Research Rule 5: Volatility Breakout</td>
                <td>UNKNOWN</td>
                <td class="positive">2.03%</td>
                <td>65.7%</td>
                <td>35</td>
                <td>1.22</td>
                <td>0.00</td>
                <td>3.30%</td>
                <td>31.0</td>
            </tr>
            
            <tr>
                <td>12</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">2.20%</td>
                <td>72.7%</td>
                <td>22</td>
                <td>1.43</td>
                <td>0.00</td>
                <td>2.70%</td>
                <td>29.3</td>
            </tr>
            
            <tr>
                <td>13</td>
                <td>Rule 2: Golden Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">2.19%</td>
                <td>81.8%</td>
                <td>11</td>
                <td>3.12</td>
                <td>0.00</td>
                <td>1.01%</td>
                <td>28.7</td>
            </tr>
            
            <tr>
                <td>14</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">2.15%</td>
                <td>68.2%</td>
                <td>22</td>
                <td>1.57</td>
                <td>0.00</td>
                <td>1.02%</td>
                <td>27.9</td>
            </tr>
            
            <tr>
                <td>15</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">1.32%</td>
                <td>68.4%</td>
                <td>19</td>
                <td>1.30</td>
                <td>0.00</td>
                <td>1.07%</td>
                <td>26.8</td>
            </tr>
            
            <tr>
                <td>16</td>
                <td>GitHub Rule 4: CCI Divergence</td>
                <td>UNKNOWN</td>
                <td class="positive">1.39%</td>
                <td>64.3%</td>
                <td>14</td>
                <td>1.48</td>
                <td>0.00</td>
                <td>2.22%</td>
                <td>24.0</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 525,301<br>
            • Backtest Range: 300 to 525,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
