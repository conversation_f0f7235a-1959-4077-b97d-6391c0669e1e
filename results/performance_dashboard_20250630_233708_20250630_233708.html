
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 23 Top-Performing Buy Rules</p>
        <p>Generated: 2025-06-30 23:37:08</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">23</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">53.5%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">17.0%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">58.9%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">65.8%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">7,408</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["58.9%","56.9%","46.2%","36.6%","23.8%","21.9%","17.5%","11.0%","10.9%","10.0%"],"textposition":"auto","x":["Prof Rule 10: Hull MA Trend","Professional Rule 1: Ichimoku Cloud Breakout","Rule 9: EMA Alignment","Advanced Rule 5: Donchian Channel Breakout","New Buy 5: CMF Positive","Rule 10: Volume Spike","AI Rule 3: Smart Money Flow Divergence","Volatility Rule 2: ATR Expansion Signal","AI Rule 6: Market Structure Shift","Rule 27: Structure Break Up"],"y":[58.93193071623395,56.94617652733178,46.22932766369125,36.56628977096271,23.818694542366543,21.851739123096877,17.462256593722138,10.979804522624297,10.924053313287368,10.003829571591384],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Prof Rule 10: Hull MA Trend","Professional Rule 1: Ichimoku Cloud Breakout","Rule 9: EMA Alignment","Advanced Rule 5: Donchian Channel Breakout","New Buy 5: CMF Positive","Rule 10: Volume Spike","AI Rule 3: Smart Money Flow Divergence","Volatility Rule 2: ATR Expansion Signal","AI Rule 6: Market Structure Shift","Rule 27: Structure Break Up"],"y":[729,619,757,302,541,150,200,78,245,107],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Prof Rule 10: Hull MA Trend","Professional Rule 1: Ichimoku Cloud Breakout","Rule 9: EMA Alignment","Advanced Rule 5: Donchian Channel Breakout","New Buy 5: CMF Positive","Rule 10: Volume Spike","AI Rule 3: Smart Money Flow Divergence","Volatility Rule 2: ATR Expansion Signal","AI Rule 6: Market Structure Shift","Rule 27: Structure Break Up"],"y":[418,356,441,168,323,80,111,43,146,64],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[58.93193071623395,56.94617652733178,46.22932766369125,36.56628977096271,23.818694542366543,21.851739123096877,17.462256593722138,10.979804522624297,10.924053313287368,10.003829571591384,12.48733618555071,8.740424149146493,11.062121569247495,4.208423867076621,2.2083343671487525,15.162505014517084,8.302854330070113,3.6323554362688735,12.488823731381693,4.728795668933104,6.498752635686963,2.6919656432275807,4.097083260888117],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Prof Rule 10","Professional Rule 1","Rule 9","Advanced Rule 5","New Buy 5","Rule 10","AI Rule 3","Volatility Rule 2","AI Rule 6","Rule 27","Rule 28","Rule 22","AI Rule 8","Ext Rule 5","AI Rule 10","AI Rule 1","Rule 6","Professional Rule 5","Volume Rule 5","Professional Rule 7","Professional Rule 10","Price Action Rule 3","Rule 2"],"textposition":"top center","x":[20.917655396285202,27.240636537024592,20.125473163802603,24.142631842700744,32.258652063948475,8.815561775345957,28.833071809938065,12.868452279577827,24.609285871953805,9.737357920186765,8.012280435168641,24.083160024461346,9.013973023771118,15.903896698089854,33.45717270377394,4.066680681430159,6.875410657352851,8.235052607100801,6.44291644516561,6.638096315990584,6.28105067722045,7.44146780776193,2.2451464441312035],"y":[58.93193071623395,56.94617652733178,46.22932766369125,36.56628977096271,23.818694542366543,21.851739123096877,17.462256593722138,10.979804522624297,10.924053313287368,10.003829571591384,12.48733618555071,8.740424149146493,11.062121569247495,4.208423867076621,2.2083343671487525,15.162505014517084,8.302854330070113,3.6323554362688735,12.488823731381693,4.728795668933104,6.498752635686963,2.6919656432275807,4.097083260888117],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["58.9%","16.3%","16.0%","11.4%"],"textposition":"auto","x":["PROFESSIONAL","UNKNOWN","ORIGINAL","AI_GENERATED"],"y":[58.93193071623395,16.256008234586016,15.95894204057642,11.363854171584567],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["1147","975","1198","470","864","230","311","121","391","171"],"textposition":"auto","x":["Prof Rule 10: Hull MA Trend","Professional Rule 1: Ichimoku Cloud Breakout","Rule 9: EMA Alignment","Advanced Rule 5: Donchian Channel Breakout","New Buy 5: CMF Positive","Rule 10: Volume Spike","AI Rule 3: Smart Money Flow Divergence","Volatility Rule 2: ATR Expansion Signal","AI Rule 6: Market Structure Shift","Rule 27: Structure Break Up"],"y":[1147,975,1198,470,864,230,311,121,391,171],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Prof Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147],"y":[0,0.0513791898136303,0.1027583796272606,0.15413756944089088,0.2055167592545212,0.25689594906815144,0.30827513888178176,0.3596543286954121,0.4110335185090424,0.46241270832267267,0.5137918981363029,0.5651710879499333,0.6165502777635635,0.6679294675771938,0.7193086573908242,0.7706878472044544,0.8220670370180848,0.873446226831715,0.9248254166453453,0.9762046064589756,1.0275837962726058,1.0789629860862362,1.1303421758998666,1.1817213657134968,1.233100555527127,1.2844797453407575,1.3358589351543877,1.387238124968018,1.4386173147816483,1.4899965045952785,1.5413756944089088,1.5927548842225392,1.6441340740361696,1.6955132638497998,1.74689245366343,1.7982716434770603,1.8496508332906907,1.901030023104321,1.952409212917951,2.0037884027315815,2.0551675925452115,2.106546782358842,2.1579259721724724,2.2093051619861024,2.2606843517997333,2.3120635416133637,2.3634427314269937,2.414821921240624,2.466201111054254,2.5175803008678845,2.568959490681515,2.620338680495145,2.6717178703087754,2.723097060122406,2.774476249936036,2.8258554397496662,2.8772346295632967,2.9286138193769267,2.979993009190557,3.0313721990041875,3.0827513888178175,3.1341305786314484,3.1855097684450784,3.236888958258709,3.2882681480723392,3.3396473378859692,3.3910265276995997,3.44240571751323,3.49378490732686,3.5451640971404905,3.5965432869541205,3.647922476767751,3.6993016665813814,3.7506808563950114,3.802060046208642,3.8534392360222722,3.904818425835902,3.9561976156495327,4.007576805463163,4.0589559952767935,4.110335185090423,4.1617143749040535,4.213093564717684,4.264472754531314,4.315851944344945,4.367231134158575,4.418610323972205,4.469989513785836,4.5213687035994665,4.572747893413097,4.624127083226727,4.675506273040357,4.726885462853987,4.778264652667618,4.829643842481248,4.881023032294879,4.932402222108508,4.983781411922139,5.035160601735769,5.0865397915493995,5.13791898136303,5.18929817117666,5.24067736099029,5.29205655080392,5.343435740617551,5.394814930431181,5.446194120244812,5.497573310058442,5.548952499872072,5.600331689685702,5.6517108794993325,5.703090069312963,5.754469259126593,5.805848448940223,5.857227638753853,5.908606828567484,5.959986018381114,6.011365208194745,6.062744398008375,6.114123587822005,6.165502777635635,6.2168819674492655,6.268261157262897,6.319640347076527,6.371019536890157,6.422398726703787,6.473777916517418,6.525157106331048,6.5765362961446785,6.627915485958308,6.6792946757719385,6.730673865585569,6.782053055399199,6.83343224521283,6.88481143502646,6.93619062484009,6.98756981465372,7.038949004467351,7.090328194280981,7.1417073840946115,7.193086573908241,7.2444657637218715,7.295844953535502,7.347224143349132,7.398603333162763,7.449982522976394,7.501361712790023,7.552740902603654,7.604120092417284,7.655499282230915,7.7068784720445445,7.758257661858175,7.809636851671804,7.861016041485436,7.912395231299065,7.963774421112697,8.015153610926326,8.066532800739957,8.117911990553587,8.169291180367217,8.220670370180846,8.272049559994478,8.323428749808107,8.37480793962174,8.426187129435368,8.477566319248998,8.528945509062629,8.58032469887626,8.63170388868989,8.68308307850352,8.73446226831715,8.78584145813078,8.83722064794441,8.888599837758042,8.939979027571672,8.991358217385303,9.042737407198933,9.094116597012562,9.145495786826194,9.196874976639823,9.248254166453455,9.299633356267083,9.351012546080714,9.402391735894344,9.453770925707975,9.505150115521605,9.556529305335236,9.607908495148864,9.659287684962496,9.710666874776125,9.762046064589757,9.813425254403386,9.864804444217016,9.916183634030647,9.967562823844277,10.018942013657908,10.070321203471538,10.121700393285169,10.173079583098799,10.224458772912428,10.27583796272606,10.327217152539689,10.37859634235332,10.42997553216695,10.48135472198058,10.53273391179421,10.58411310160784,10.635492291421471,10.686871481235102,10.738250671048732,10.789629860862362,10.841009050675993,10.892388240489623,10.943767430303254,10.995146620116884,11.046525809930515,11.097904999744143,11.149284189557775,11.200663379371404,11.252042569185036,11.303421758998665,11.354800948812295,11.406180138625926,11.457559328439556,11.508938518253187,11.560317708066817,11.611696897880446,11.663076087694078,11.714455277507707,11.765834467321339,11.817213657134968,11.868592846948598,11.919972036762228,11.971351226575859,12.02273041638949,12.07410960620312,12.12548879601675,12.17686798583038,12.22824717564401,12.279626365457641,12.33100555527127,12.382384745084902,12.433763934898531,12.485143124712161,12.536522314525794,12.587901504339422,12.639280694153054,12.690659883966683,12.742039073780314,12.793418263593944,12.844797453407574,12.896176643221205,12.947555833034835,12.998935022848464,13.050314212662096,13.101693402475725,13.153072592289357,13.204451782102986,13.255830971916616,13.307210161730247,13.358589351543877,13.409968541357507,13.461347731171138,13.512726920984768,13.564106110798399,13.615485300612027,13.66686449042566,13.718243680239288,13.76962287005292,13.821002059866549,13.87238124968018,13.92376043949381,13.97513962930744,14.02651881912107,14.077898008934701,14.12927719874833,14.180656388561962,14.23203557837559,14.283414768189223,14.334793958002853,14.386173147816482,14.437552337630114,14.488931527443743,14.540310717257375,14.591689907071004,14.643069096884636,14.694448286698265,14.745827476511895,14.797206666325526,14.848585856139154,14.899965045952788,14.951344235766417,15.002723425580045,15.054102615393676,15.105481805207308,15.156860995020939,15.208240184834567,15.259619374648198,15.31099856446183,15.362377754275458,15.413756944089089,15.465136133902718,15.51651532371635,15.56789451352998,15.619273703343609,15.67065289315724,15.722032082970872,15.773411272784502,15.82479046259813,15.87616965241176,15.927548842225393,15.978928032039022,16.030307221852652,16.081686411666283,16.133065601479913,16.184444791293544,16.235823981107174,16.287203170920804,16.338582360734435,16.389961550548065,16.441340740361692,16.492719930175326,16.544099119988957,16.595478309802587,16.646857499616214,16.698236689429848,16.74961587924348,16.800995069057105,16.852374258870736,16.90375344868437,16.955132638497997,17.006511828311627,17.057891018125257,17.10927020793889,17.16064939775252,17.21202858756615,17.26340777737978,17.31478696719341,17.36616615700704,17.41754534682067,17.4689245366343,17.52030372644793,17.57168291626156,17.623062106075192,17.67444129588882,17.725820485702453,17.777199675516083,17.82857886532971,17.879958055143344,17.931337244956975,17.982716434770605,18.034095624584232,18.085474814397866,18.136854004211497,18.188233194025123,18.239612383838754,18.290991573652388,18.342370763466015,18.393749953279645,18.445129143093276,18.49650833290691,18.547887522720536,18.599266712534167,18.650645902347797,18.702025092161428,18.753404281975058,18.80478347178869,18.85616266160232,18.90754185141595,18.95892104122958,19.01030023104321,19.061679420856837,19.11305861067047,19.1644378004841,19.21581699029773,19.26719618011136,19.318575369924993,19.369954559738623,19.42133374955225,19.47271293936588,19.524092129179515,19.57547131899314,19.626850508806772,19.678229698620406,19.729608888434033,19.780988078247663,19.832367268061294,19.883746457874928,19.935125647688555,19.986504837502185,20.037884027315815,20.089263217129446,20.140642406943076,20.192021596756707,20.243400786570337,20.294779976383968,20.346159166197598,20.39753835601123,20.448917545824855,20.50029673563849,20.55167592545212,20.603055115265747,20.654434305079377,20.70581349489301,20.75719268470664,20.80857187452027,20.8599510643339,20.911330254147533,20.96270944396116,21.01408863377479,21.06546782358842,21.116847013402055,21.16822620321568,21.219605393029312,21.270984582842942,21.322363772656573,21.373742962470203,21.425122152283834,21.476501342097464,21.527880531911094,21.579259721724725,21.630638911538355,21.682018101351986,21.733397291165616,21.784776480979247,21.836155670792873,21.887534860606507,21.938914050420138,21.99029324023377,22.041672430047395,22.09305161986103,22.14443080967466,22.195809999488286,22.247189189301917,22.29856837911555,22.349947568929178,22.401326758742808,22.45270594855644,22.504085138370073,22.5554643281837,22.60684351799733,22.65822270781096,22.70960189762459,22.76098108743822,22.81236027725185,22.863739467065482,22.915118656879113,22.966497846692743,23.017877036506373,23.06925622632,23.120635416133634,23.172014605947265,23.22339379576089,23.274772985574526,23.326152175388156,23.377531365201786,23.428910555015413,23.480289744829047,23.531668934642678,23.583048124456305,23.634427314269935,23.68580650408357,23.737185693897196,23.788564883710826,23.839944073524457,23.89132326333809,23.942702453151718,23.994081642965348,24.04546083277898,24.09684002259261,24.14821921240624,24.19959840221987,24.2509775920335,24.30235678184713,24.35373597166076,24.40511516147439,24.45649435128802,24.507873541101652,24.559252730915283,24.61063192072891,24.66201111054254,24.713390300356174,24.764769490169805,24.81614867998343,24.867527869797062,24.918907059610696,24.970286249424323,25.021665439237953,25.073044629051587,25.124423818865214,25.175803008678844,25.227182198492475,25.27856138830611,25.329940578119736,25.381319767933366,25.432698957746997,25.484078147560627,25.535457337374257,25.586836527187888,25.63821571700152,25.68959490681515,25.74097409662878,25.79235328644241,25.843732476256037,25.89511166606967,25.9464908558833,25.997870045696928,26.04924923551056,26.100628425324192,26.152007615137823,26.20338680495145,26.25476599476508,26.306145184578714,26.35752437439234,26.40890356420597,26.4602827540196,26.511661943833232,26.563041133646863,26.614420323460493,26.665799513274123,26.717178703087754,26.768557892901384,26.819937082715015,26.871316272528645,26.922695462342276,26.974074652155906,27.025453841969536,27.076833031783167,27.128212221596797,27.179591411410428,27.230970601224055,27.28234979103769,27.33372898085132,27.385108170664946,27.436487360478576,27.48786655029221,27.53924574010584,27.590624929919468,27.642004119733098,27.693383309546732,27.74476249936036,27.79614168917399,27.84752087898762,27.89890006880125,27.95027925861488,28.00165844842851,28.05303763824214,28.104416828055772,28.155796017869402,28.207175207683033,28.25855439749666,28.309933587310294,28.361312777123924,28.412691966937555,28.46407115675118,28.515450346564815,28.566829536378446,28.618208726192073,28.669587916005707,28.720967105819337,28.772346295632964,28.823725485446595,28.87510467526023,28.92648386507386,28.977863054887486,29.029242244701116,29.08062143451475,29.132000624328377,29.183379814142008,29.234759003955638,29.286138193769272,29.3375173835829,29.38889657339653,29.44027576321016,29.49165495302379,29.543034142837417,29.59441333265105,29.64579252246468,29.69717171227831,29.748550902091942,29.799930091905576,29.8513092817192,29.902688471532834,29.95406766134646,30.00544685116009,30.056826040973725,30.108205230787352,30.159584420600986,30.210963610414616,30.262342800228243,30.313721990041877,30.365101179855507,30.416480369669134,30.46785955948277,30.519238749296395,30.570617939110026,30.62199712892366,30.673376318737287,30.724755508550917,30.77613469836455,30.827513888178178,30.87889307799181,30.930272267805435,30.98165145761907,31.0330306474327,31.084409837246326,31.13578902705996,31.187168216873594,31.238547406687218,31.28992659650085,31.34130578631448,31.39268497612811,31.444064165941743,31.49544335575537,31.546822545569004,31.598201735382634,31.64958092519626,31.700960115009895,31.75233930482352,31.803718494637153,31.855097684450786,31.906476874264413,31.957856064078044,32.009235253891674,32.060614443705305,32.111993633518935,32.163372823332566,32.214752013146196,32.266131202959826,32.31751039277346,32.36888958258709,32.42026877240072,32.47164796221435,32.52302715202798,32.57440634184161,32.62578553165524,32.67716472146887,32.72854391128249,32.77992310109613,32.83130229090976,32.882681480723384,32.93406067053702,32.98543986035065,33.036819050164276,33.08819823997791,33.13957742979154,33.190956619605174,33.242335809418805,33.29371499923243,33.345094189046065,33.396473378859696,33.44785256867332,33.49923175848696,33.55061094830058,33.60199013811421,33.65336932792785,33.70474851774147,33.7561277075551,33.80750689736874,33.85888608718236,33.91026527699599,33.96164446680963,34.013023656623254,34.06440284643689,34.115782036250515,34.167161226064145,34.21854041587778,34.269919605691406,34.32129879550504,34.372677985318674,34.4240571751323,34.47543636494593,34.52681555475956,34.57819474457319,34.62957393438682,34.68095312420045,34.73233231401408,34.78371150382771,34.83509069364134,34.88646988345497,34.9378490732686,34.98922826308223,35.04060745289586,35.09198664270949,35.14336583252312,35.194745022336754,35.246124212150384,35.297503401964015,35.34888259177764,35.400261781591276,35.451640971404906,35.50302016121853,35.55439935103217,35.6057785408458,35.65715773065942,35.70853692047306,35.75991611028669,35.81129530010032,35.86267448991395,35.91405367972757,35.96543286954121,36.01681205935484,36.068191249168464,36.1195704389821,36.17094962879573,36.222328818609355,36.27370800842299,36.325087198236616,36.37646638805025,36.427845577863884,36.47922476767751,36.53060395749114,36.581983147304776,36.6333623371184,36.68474152693203,36.73612071674566,36.78749990655929,36.83887909637293,36.89025828618655,36.94163747600018,36.99301666581382,37.04439585562744,37.09577504544107,37.1471542352547,37.198533425068334,37.249912614881964,37.301291804695595,37.352670994509225,37.404050184322855,37.455429374136486,37.506808563950116,37.55818775376375,37.60956694357738,37.66094613339101,37.71232532320464,37.76370451301827,37.8150837028319,37.86646289264553,37.91784208245916,37.96922127227279,38.02060046208642,38.07197965190005,38.123358841713674,38.17473803152731,38.22611722134094,38.277496411154566,38.3288756009682,38.380254790781834,38.43163398059546,38.483013170409095,38.53439236022272,38.585771550036355,38.637150739849986,38.68852992966361,38.73990911947725,38.79128830929088,38.8426674991045,38.89404668891814,38.94542587873176,38.99680506854539,39.04818425835903,39.09956344817265,39.15094263798628,39.20232182779992,39.253701017613544,39.305080207427174,39.35645939724081,39.407838587054435,39.459217776868066,39.510596966681696,39.56197615649533,39.613355346308964,39.66473453612259,39.71611372593622,39.767492915749855,39.81887210556348,39.87025129537711,39.92163048519074,39.97300967500437,40.024388864818,40.07576805463163,40.12714724444526,40.17852643425889,40.22990562407252,40.28128481388615,40.33266400369978,40.38404319351341,40.435422383327044,40.486801573140674,40.538180762954305,40.589559952767935,40.640939142581566,40.692318332395196,40.74369752220882,40.79507671202246,40.84645590183609,40.89783509164971,40.94921428146335,41.00059347127698,41.0519726610906,41.10335185090424,41.15473104071787,41.20611023053149,41.25748942034513,41.308868610158754,41.36024779997239,41.41162698978602,41.463006179599645,41.51438536941328,41.56576455922691,41.61714374904054,41.668522938854174,41.7199021286678,41.77128131848143,41.822660508295066,41.87403969810869,41.92541888792232,41.97679807773596,42.02817726754958,42.07955645736321,42.13093564717684,42.18231483699047,42.23369402680411,42.28507321661773,42.33645240643136,42.387831596245,42.439210786058624,42.490589975872254,42.541969165685884,42.593348355499515,42.644727545313145,42.696106735126776,42.747485924940406,42.79886511475404,42.85024430456767,42.9016234943813,42.95300268419493,43.00438187400856,43.05576106382219,43.10714025363582,43.15851944344945,43.20989863326308,43.26127782307671,43.31265701289034,43.36403620270397,43.4154153925176,43.46679458233123,43.518173772144856,43.56955296195849,43.620932151772124,43.67231134158575,43.723690531399384,43.775069721213015,43.82644891102664,43.877828100840276,43.9292072906539,43.98058648046754,44.03196567028117,44.08334486009479,44.13472404990843,44.18610323972206,44.23748242953568,44.28886161934932,44.34024080916294,44.39161999897657,44.44299918879021,44.494378378603834,44.545757568417464,44.5971367582311,44.648515948044725,44.699895137858356,44.75127432767199,44.802653517485616,44.85403270729925,44.90541189711288,44.95679108692651,45.008170276740145,45.05954946655377,45.1109286563674,45.16230784618104,45.21368703599466,45.26506622580829,45.31644541562192,45.36782460543555,45.41920379524918,45.47058298506281,45.52196217487644,45.57334136469007,45.6247205545037,45.676099744317334,45.727478934130964,45.778858123944595,45.830237313758225,45.881616503571856,45.932995693385486,45.984374883199116,46.03575407301275,46.08713326282638,46.13851245264,46.18989164245364,46.24127083226727,46.29265002208089,46.34402921189453,46.39540840170816,46.44678759152178,46.49816678133542,46.54954597114905,46.600925160962674,46.65230435077631,46.703683540589935,46.75506273040357,46.8064419202172,46.85782111003083,46.909200299844464,46.960579489658095,47.01195867947172,47.063337869285355,47.11471705909898,47.16609624891261,47.21747543872625,47.26885462853987,47.3202338183535,47.37161300816714,47.42299219798076,47.47437138779439,47.52575057760802,47.57712976742165,47.62850895723528,47.67988814704891,47.731267336862544,47.78264652667618,47.834025716489805,47.885404906303435,47.936784096117066,47.988163285930696,48.03954247574433,48.09092166555796,48.14230085537159,48.19368004518522,48.24505923499885,48.29643842481248,48.34781761462611,48.39919680443974,48.45057599425337,48.501955184067,48.55333437388063,48.60471356369426,48.65609275350789,48.70747194332152,48.75885113313515,48.81023032294878,48.86160951276241,48.91298870257604,48.964367892389674,49.015747082203305,49.06712627201693,49.118505461830566,49.169884651644196,49.22126384145782,49.27264303127146,49.32402222108508,49.37540141089871,49.42678060071235,49.47815979052597,49.52953898033961,49.58091817015324,49.63229735996686,49.6836765497805,49.735055739594124,49.786434929407754,49.83781411922139,49.889193309035015,49.940572498848645,49.99195168866228,50.043330878475906,50.09471006828954,50.146089258103174,50.1974684479168,50.24884763773043,50.30022682754406,50.35160601735769,50.40298520717133,50.45436439698495,50.50574358679858,50.55712277661222,50.60850196642584,50.65988115623947,50.7112603460531,50.76263953586673,50.81401872568036,50.86539791549399,50.916777105307624,50.968156295121254,51.019535484934885,51.070914674748515,51.12229386456214,51.173673054375776,51.225052244189406,51.27643143400304,51.32781062381667,51.3791898136303,51.43056900344393,51.48194819325756,51.53332738307118,51.58470657288482,51.63608576269845,51.68746495251207,51.73884414232571,51.79022333213934,51.841602521952964,51.8929817117666,51.94436090158023,51.995740091393856,52.04711928120749,52.09849847102112,52.149877660834754,52.201256850648384,52.25263604046201,52.304015230275645,52.355394420089276,52.4067736099029,52.45815279971654,52.50953198953016,52.56091117934379,52.61229036915743,52.66366955897105,52.71504874878468,52.76642793859832,52.81780712841194,52.86918631822557,52.9205655080392,52.971944697852834,53.023323887666464,53.074703077480095,53.126082267293725,53.17746145710736,53.228840646920986,53.28021983673462,53.33159902654825,53.38297821636188,53.43435740617551,53.48573659598914,53.53711578580277,53.5884949756164,53.63987416543003,53.69125335524366,53.74263254505729,53.79401173487092,53.84539092468455,53.89677011449818,53.94814930431181,53.99952849412544,54.05090768393907,54.1022868737527,54.153666063566334,54.205045253379964,54.256424443193595,54.30780363300722,54.359182822820856,54.410562012634486,54.46194120244811,54.51332039226175,54.56469958207538,54.616078771889,54.66745796170264,54.71883715151626,54.77021634132989,54.82159553114353,54.87297472095715,54.92435391077079,54.97573310058442,55.027112290398044,55.07849148021168,55.129870670025305,55.181249859838935,55.23262904965257,55.284008239466196,55.33538742927983,55.386766619093464,55.43814580890709,55.48952499872072,55.540904188534356,55.59228337834798,55.64366256816161,55.69504175797524,55.74642094778887,55.7978001376025,55.84917932741613,55.90055851722976,55.9519377070434,56.00331689685702,56.05469608667065,56.10607527648428,56.157454466297914,56.208833656111544,56.260212845925174,56.311592035738805,56.362971225552435,56.414350415366066,56.465729605179696,56.51710879499332,56.56848798480696,56.61986717462059,56.67124636443422,56.72262555424785,56.77400474406148,56.82538393387511,56.87676312368874,56.92814231350236,56.979521503316,57.03090069312963,57.082279882943254,57.13365907275689,57.18503826257052,57.236417452384146,57.28779664219778,57.33917583201141,57.39055502182504,57.441934211638674,57.4933134014523,57.54469259126593,57.596071781079566,57.64745097089319,57.69883016070683,57.75020935052046,57.80158854033408,57.85296773014772,57.90434691996134,57.95572610977497,58.00710529958861,58.05848448940223,58.10986367921586,58.1612428690295,58.212622058843124,58.264001248656754,58.315380438470385,58.366759628284015,58.418138818097646,58.469518007911276,58.520897197724906,58.572276387538544,58.62365557735217,58.6750347671658,58.72641395697943,58.77779314679306,58.82917233660669,58.88055152642032,58.93193071623395],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Professional Rule 1","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975],"y":[0,0.05840633489982747,0.11681266979965493,0.1752190046994824,0.23362533959930987,0.2920316744991373,0.3504380093989648,0.40884434429879224,0.46725067919861973,0.5256570140984472,0.5840633489982746,0.6424696838981022,0.7008760187979296,0.7592823536977571,0.8176886885975845,0.8760950234974121,0.9345013583972395,0.9929076932970669,1.0513140281968945,1.1097203630967218,1.1681266979965492,1.2265330328963768,1.2849393677962044,1.3433457026960318,1.4017520375958592,1.4601583724956866,1.5185647073955142,1.5769710422953416,1.635377377195169,1.6937837120949966,1.7521900469948242,1.8105963818946516,1.869002716794479,1.9274090516943063,1.9858153865941337,2.0442217214939613,2.102628056393789,2.1610343912936165,2.2194407261934437,2.2778470610932713,2.3362533959930984,2.394659730892926,2.4530660657927537,2.511472400692581,2.569878735592409,2.628285070492236,2.6866914053920636,2.745097740291891,2.8035040751917184,2.861910410091546,2.920316744991373,2.9787230798912008,3.0371294147910284,3.095535749690856,3.153942084590683,3.2123484194905108,3.270754754390338,3.3291610892901655,3.387567424189993,3.4459737590898203,3.5043800939896483,3.5627864288894755,3.621192763789303,3.6795990986891303,3.738005433588958,3.796411768488785,3.8548181033886126,3.9132244382884402,3.9716307731882674,4.030037108088095,4.088443442987923,4.146849777887749,4.205256112787578,4.263662447687405,4.322068782587233,4.380475117487061,4.438881452386887,4.497287787286715,4.555694122186543,4.61410045708637,4.672506791986197,4.7309131268860245,4.789319461785852,4.84772579668568,4.906132131585507,4.964538466485334,5.022944801385162,5.081351136284989,5.139757471184818,5.1981638060846445,5.256570140984472,5.3149764758843,5.373382810784127,5.431789145683955,5.490195480583782,5.548601815483609,5.607008150383437,5.665414485283264,5.723820820183092,5.782227155082919,5.840633489982746,5.899039824882574,5.9574461597824016,6.015852494682228,6.074258829582057,6.132665164481884,6.191071499381712,6.249477834281539,6.307884169181366,6.366290504081194,6.4246968389810215,6.483103173880849,6.541509508780676,6.599915843680503,6.658322178580331,6.716728513480159,6.775134848379986,6.833541183279813,6.891947518179641,6.950353853079468,7.008760187979297,7.067166522879123,7.125572857778951,7.183979192678778,7.242385527578606,7.300791862478433,7.3591981973782605,7.417604532278087,7.476010867177916,7.534417202077744,7.59282353697757,7.651229871877399,7.709636206777225,7.768042541677054,7.8264488765768805,7.884855211476708,7.943261546376535,8.001667881276363,8.06007421617619,8.118480551076019,8.176886885975845,8.235293220875674,8.293699555775499,8.352105890675327,8.410512225575156,8.468918560474982,8.52732489537481,8.585731230274638,8.644137565174466,8.702543900074293,8.760950234974121,8.819356569873946,8.877762904773775,8.936169239673601,8.99457557457343,9.052981909473257,9.111388244373085,9.169794579272912,9.22820091417274,9.286607249072567,9.345013583972394,9.403419918872222,9.461826253772049,9.520232588671877,9.578638923571704,9.637045258471533,9.69545159337136,9.753857928271188,9.812264263171015,9.870670598070841,9.929076932970668,9.987483267870497,10.045889602770323,10.104295937670152,10.162702272569978,10.221108607469807,10.279514942369635,10.337921277269462,10.396327612169289,10.454733947069116,10.513140281968944,10.57154661686877,10.6299529517686,10.688359286668426,10.746765621568255,10.805171956468081,10.86357829136791,10.921984626267736,10.980390961167563,11.03879729606739,11.097203630967218,11.155609965867045,11.214016300766874,11.272422635666702,11.330828970566529,11.389235305466357,11.447641640366184,11.50604797526601,11.564454310165837,11.622860645065666,11.681266979965493,11.739673314865321,11.798079649765148,11.856485984664976,11.914892319564803,11.97329865446463,12.031704989364457,12.090111324264285,12.148517659164114,12.20692399406394,12.265330328963769,12.323736663863595,12.382142998763424,12.44054933366325,12.498955668563077,12.557362003462904,12.615768338362733,12.67417467326256,12.732581008162388,12.790987343062215,12.849393677962043,12.90780001286187,12.966206347761698,13.024612682661525,13.083019017561352,13.14142535246118,13.199831687361007,13.258238022260835,13.316644357160662,13.37505069206049,13.433457026960317,13.491863361860146,13.550269696759973,13.6086760316598,13.667082366559626,13.725488701459454,13.783895036359281,13.84230137125911,13.900707706158936,13.959114041058765,14.017520375958593,14.07592671085842,14.134333045758247,14.192739380658074,14.251145715557902,14.309552050457729,14.367958385357555,14.426364720257386,14.484771055157212,14.54317739005704,14.601583724956866,14.659990059856694,14.718396394756521,14.776802729656348,14.835209064556174,14.893615399456005,14.952021734355831,15.010428069255658,15.068834404155488,15.127240739055315,15.18564707395514,15.244053408854967,15.302459743754797,15.360866078654624,15.41927241355445,15.477678748454277,15.536085083354108,15.594491418253934,15.652897753153761,15.711304088053588,15.769710422953416,15.828116757853243,15.88652309275307,15.9449294276529,16.003335762552727,16.06174209745255,16.12014843235238,16.17855476725221,16.236961102152037,16.295367437051862,16.35377377195169,16.41218010685152,16.470586441751347,16.528992776651172,16.587399111550997,16.64580544645083,16.704211781350654,16.762618116250483,16.82102445115031,16.87943078605014,16.937837120949965,16.996243455849793,17.05464979074962,17.113056125649447,17.171462460549275,17.2298687954491,17.288275130348932,17.346681465248757,17.405087800148586,17.46349413504841,17.521900469948243,17.580306804848068,17.638713139747892,17.69711947464772,17.75552580954755,17.813932144447378,17.872338479347203,17.930744814247035,17.98915114914686,18.04755748404669,18.105963818946513,18.164370153846342,18.22277648874617,18.281182823645995,18.339589158545824,18.397995493445652,18.45640182834548,18.514808163245306,18.573214498145134,18.631620833044963,18.690027167944788,18.748433502844616,18.806839837744445,18.865246172644273,18.923652507544098,18.982058842443926,19.040465177343755,19.098871512243583,19.15727784714341,19.215684182043233,19.274090516943065,19.33249685184289,19.39090318674272,19.449309521642544,19.507715856542376,19.5661221914422,19.62452852634203,19.682934861241858,19.741341196141683,19.79974753104151,19.858153865941336,19.916560200841168,19.974966535740993,20.03337287064082,20.091779205540647,20.15018554044048,20.208591875340304,20.26699821024013,20.325404545139957,20.383810880039785,20.442217214939614,20.50062354983944,20.55902988473927,20.617436219639096,20.675842554538924,20.73424888943875,20.792655224338578,20.851061559238406,20.90946789413823,20.96787422903806,21.02628056393789,21.084686898837717,21.14309323373754,21.20149956863737,21.2599059035372,21.318312238437024,21.376718573336852,21.435124908236677,21.49353124313651,21.551937578036334,21.610343912936163,21.66875024783599,21.72715658273582,21.785562917635644,21.843969252535473,21.9023755874353,21.960781922335126,22.019188257234955,22.07759459213478,22.136000927034612,22.194407261934437,22.252813596834265,22.31121993173409,22.36962626663392,22.428032601533747,22.486438936433572,22.544845271333404,22.60325160623323,22.661657941133058,22.720064276032883,22.778470610932715,22.83687694583254,22.895283280732368,22.953689615632193,23.01209595053202,23.07050228543185,23.128908620331675,23.187314955231503,23.245721290131332,23.30412762503116,23.362533959930985,23.420940294830814,23.479346629730642,23.537752964630467,23.596159299530296,23.654565634430124,23.712971969329953,23.771378304229778,23.829784639129606,23.888190974029435,23.94659730892926,24.005003643829088,24.063409978728913,24.121816313628745,24.18022264852857,24.2386289834284,24.297035318328227,24.355441653228056,24.41384798812788,24.47225432302771,24.530660657927537,24.589066992827362,24.64747332772719,24.705879662627016,24.764285997526848,24.822692332426673,24.8810986673265,24.939505002226326,24.997911337126155,25.056317672025983,25.114724006925808,25.173130341825637,25.231536676725465,25.289943011625294,25.34834934652512,25.40675568142495,25.465162016324776,25.523568351224604,25.58197468612443,25.640381021024258,25.698787355924086,25.75719369082391,25.81560002572374,25.874006360623568,25.932412695523396,25.99081903042322,26.04922536532305,26.10763170022288,26.166038035122703,26.224444370022532,26.28285070492236,26.34125703982219,26.399663374722014,26.458069709621842,26.51647604452167,26.5748823794215,26.633288714321324,26.69169504922115,26.75010138412098,26.808507719020806,26.866914053920635,26.92532038882046,26.98372672372029,27.042133058620117,27.100539393519945,27.158945728419774,27.2173520633196,27.275758398219427,27.334164733119252,27.392571068019084,27.45097740291891,27.509383737818737,27.567790072718562,27.626196407618394,27.68460274251822,27.743009077418044,27.801415412317873,27.8598217472177,27.91822808211753,27.976634417017355,28.035040751917187,28.09344708681701,28.15185342171684,28.210259756616665,28.268666091516494,28.327072426416322,28.385478761316147,28.443885096215975,28.502291431115804,28.56069776601563,28.619104100915457,28.677510435815286,28.73591677071511,28.79432310561494,28.85272944051477,28.911135775414593,28.969542110314425,29.027948445214253,29.08635478011408,29.144761115013907,29.20316744991373,29.26157378481356,29.31998011971339,29.378386454613214,29.436792789513042,29.495199124412874,29.553605459312696,29.612011794212528,29.67041812911235,29.72882446401218,29.78723079891201,29.845637133811834,29.904043468711663,29.96244980361149,30.020856138511316,30.079262473411145,30.137668808310977,30.1960751432108,30.25448147811063,30.31288781301045,30.37129414791028,30.429700482810112,30.488106817709934,30.546513152609766,30.604919487509594,30.66332582240942,30.721732157309248,30.780138492209073,30.8385448271089,30.89695116200873,30.955357496908555,31.013763831808383,31.072170166708215,31.130576501608036,31.18898283650787,31.247389171407697,31.305795506307522,31.36420184120735,31.422608176107175,31.481014511007004,31.539420845906832,31.597827180806657,31.656233515706486,31.714639850606318,31.77304618550614,31.83145252040597,31.8898588553058,31.948265190205625,32.00667152510545,32.065077860005275,32.1234841949051,32.18189052980494,32.24029686470476,32.29870319960459,32.35710953450442,32.415515869404246,32.473922204304074,32.532328539203895,32.590734874103724,32.64914120900355,32.70754754390338,32.76595387880321,32.82436021370304,32.88276654860286,32.941172883502695,32.99957921840252,33.057985553302345,33.11639188820217,33.174798223101995,33.23320455800183,33.29161089290166,33.35001722780148,33.40842356270131,33.46682989760114,33.525236232500966,33.583642567400794,33.64204890230062,33.700455237200444,33.75886157210028,33.8172679070001,33.87567424189993,33.93408057679976,33.99248691169959,34.050893246599415,34.10929958149924,34.167705916399065,34.22611225129889,34.28451858619872,34.34292492109855,34.40133125599838,34.4597375908982,34.518143925798036,34.576550260697864,34.634956595597686,34.693362930497514,34.75176926539734,34.81017560029717,34.868581935197,34.92698827009682,34.98539460499665,35.043800939896485,35.10220727479631,35.160613609696135,35.21901994459596,35.277426279495785,35.33583261439562,35.39423894929544,35.45264528419527,35.5110516190951,35.56945795399493,35.627864288894756,35.686270623794584,35.744676958694406,35.803083293594234,35.86148962849407,35.91989596339389,35.97830229829372,36.03670863319354,36.09511496809338,36.153521302993205,36.21192763789303,36.270333972792855,36.328740307692684,36.38714664259251,36.44555297749234,36.50395931239217,36.56236564729199,36.620771982191826,36.67917831709165,36.737584651991476,36.795990986891304,36.854397321791126,36.91280365669096,36.97120999159079,37.02961632649061,37.08802266139044,37.14642899629027,37.2048353311901,37.263241666089925,37.32164800098975,37.380054335889575,37.43846067078941,37.49686700568923,37.55527334058906,37.61367967548889,37.67208601038872,37.730492345288546,37.78889868018837,37.847305015088196,37.905711349988024,37.96411768488785,38.02252401978768,38.08093035468751,38.13933668958733,38.19774302448717,38.25614935938699,38.31455569428682,38.372962029186645,38.43136836408647,38.4897746989863,38.54818103388613,38.60658736878595,38.66499370368578,38.723400038585616,38.78180637348544,38.840212708385266,38.89861904328509,38.957025378184916,39.01543171308475,39.07383804798457,39.1322443828844,39.19065071778423,39.24905705268406,39.30746338758389,39.365869722483716,39.42427605738354,39.482682392283365,39.541088727183194,39.59949506208302,39.65790139698285,39.71630773188267,39.77471406678251,39.833120401682336,39.89152673658216,39.949933071481986,40.008339406381815,40.06674574128164,40.12515207618147,40.18355841108129,40.24196474598112,40.30037108088096,40.35877741578078,40.41718375068061,40.475590085580436,40.53399642048026,40.59240275538009,40.650809090279914,40.70921542517974,40.76762176007957,40.8260280949794,40.88443442987923,40.942840764779056,41.00124709967888,41.059653434578706,41.11805976947854,41.17646610437836,41.23487243927819,41.29327877417801,41.35168510907785,41.41009144397768,41.4684977788775,41.52690411377733,41.585310448677156,41.643716783576984,41.70212311847681,41.760529453376634,41.81893578827646,41.8773421231763,41.93574845807612,41.99415479297595,42.05256112787578,42.1109674627756,42.16937379767543,42.22778013257526,42.28618646747508,42.34459280237491,42.40299913727474,42.46140547217457,42.5198118070744,42.57821814197422,42.63662447687405,42.69503081177388,42.753437146673704,42.81184348157353,42.870249816473354,42.92865615137319,42.98706248627302,43.04546882117284,43.10387515607267,43.1622814909725,43.220687825872325,43.27909416077215,43.33750049567198,43.3959068305718,43.45431316547164,43.51271950037146,43.57112583527129,43.62953217017112,43.687938505070946,43.746344839970774,43.8047511748706,43.863157509770424,43.92156384467025,43.97997017957009,44.03837651446991,44.09678284936974,44.15518918426956,44.21359551916939,44.272001854069224,44.330408188969045,44.388814523868874,44.4472208587687,44.50562719366853,44.56403352856836,44.62243986346818,44.68084619836801,44.73925253326784,44.797658868167666,44.856065203067494,44.91447153796732,44.972877872867144,45.03128420776698,45.08969054266681,45.14809687756663,45.20650321246646,45.26490954736629,45.323315882266115,45.381722217165944,45.440128552065765,45.498534886965594,45.55694122186543,45.61534755676525,45.67375389166508,45.7321602265649,45.790566561464736,45.848972896364565,45.907379231264386,45.965785566164215,46.02419190106404,46.08259823596387,46.1410045708637,46.19941090576353,46.25781724066335,46.31622357556318,46.37462991046301,46.433036245362835,46.491442580262664,46.549848915162485,46.60825525006232,46.66666158496215,46.72506791986197,46.7834742547618,46.84188058966163,46.900286924561456,46.958693259461285,47.017099594361106,47.075505929260935,47.13391226416077,47.19231859906059,47.25072493396042,47.30913126886025,47.36753760376008,47.425943938659906,47.48435027355973,47.542756608459555,47.601162943359384,47.65956927825921,47.71797561315904,47.77638194805887,47.83478828295869,47.89319461785852,47.951600952758355,48.010007287658176,48.068413622558005,48.126819957457826,48.18522629235766,48.24363262725749,48.30203896215731,48.36044529705714,48.41885163195697,48.4772579668568,48.535664301756626,48.594070636656454,48.652476971556275,48.71088330645611,48.76928964135593,48.82769597625576,48.88610231115559,48.94450864605542,49.00291498095525,49.061321315855075,49.119727650754896,49.178133985654725,49.23654032055455,49.29494665545438,49.35335299035421,49.41175932525403,49.47016566015387,49.528571995053696,49.58697832995352,49.645384664853346,49.703790999753174,49.762197334653,49.82060366955283,49.87901000445265,49.93741633935248,49.99582267425231,50.05422900915214,50.11263534405197,50.171041678951795,50.229448013851616,50.28785434875145,50.34626068365127,50.4046670185511,50.46307335345093,50.52147968835076,50.57988602325059,50.638292358150416,50.69669869305024,50.755105027950066,50.8135113628499,50.87191769774972,50.93032403264955,50.98873036754937,51.04713670244921,51.10554303734904,51.16394937224886,51.22235570714869,51.280762042048515,51.339168376948344,51.39757471184817,51.455981046748,51.51438738164782,51.57279371654765,51.63120005144748,51.68960638634731,51.748012721247136,51.80641905614696,51.86482539104679,51.92323172594662,51.98163806084644,52.04004439574627,52.0984507306461,52.15685706554593,52.21526340044576,52.27366973534558,52.33207607024541,52.39048240514524,52.448888740045064,52.50729507494489,52.56570140984472,52.62410774474455,52.68251407964438,52.7409204145442,52.79932674944403,52.857733084343856,52.916139419243684,52.97454575414351,53.03295208904334,53.09135842394316,53.149764758843,53.20817109374282,53.26657742864265,53.32498376354248,53.3833900984423,53.441796433342134,53.50020276824196,53.558609103141784,53.61701543804161,53.67542177294144,53.73382810784127,53.7922344427411,53.85064077764092,53.90904711254075,53.96745344744058,54.025859782340405,54.08426611724023,54.14267245214006,54.20107878703989,54.25948512193972,54.31789145683955,54.37629779173937,54.4347041266392,54.493110461539025,54.551516796438854,54.60992313133868,54.668329466238504,54.72673580113834,54.78514213603817,54.84354847093799,54.90195480583782,54.96036114073764,55.018767475637475,55.0771738105373,55.135580145437125,55.19398648033695,55.25239281523679,55.31079915013661,55.36920548503644,55.42761181993627,55.48601815483609,55.544424489735924,55.602830824635745,55.661237159535574,55.7196434944354,55.77804982933523,55.83645616423506,55.89486249913489,55.95326883403471,56.01167516893454,56.07008150383437,56.128487838734195,56.18689417363402,56.245300508533845,56.30370684343368,56.36211317833351,56.42051951323333,56.47892584813316,56.53733218303299,56.595738517932816,56.654144852832644,56.712551187732466,56.770957522632294,56.82936385753213,56.88777019243195,56.94617652733178],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Rule 9","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198],"y":[0,0.03858875431025981,0.07717750862051961,0.11576626293077942,0.15435501724103923,0.19294377155129905,0.23153252586155884,0.2701212801718187,0.30871003448207845,0.3472987887923383,0.3858875431025981,0.42447629741285786,0.4630650517231177,0.5016538060333775,0.5402425603436374,0.5788313146538971,0.6174200689641569,0.6560088232744168,0.6945975775846766,0.7331863318949363,0.7717750862051962,0.810363840515456,0.8489525948257157,0.8875413491359756,0.9261301034462354,0.9647188577564952,1.003307612066755,1.0418963663770149,1.0804851206872748,1.1190738749975344,1.1576626293077943,1.1962513836180542,1.2348401379283138,1.2734288922385737,1.3120176465488336,1.3506064008590934,1.389195155169353,1.427783909479613,1.4663726637898726,1.5049614181001327,1.5435501724103924,1.5821389267206523,1.620727681030912,1.6593164353411718,1.6979051896514314,1.7364939439616915,1.7750826982719512,1.813671452582211,1.8522602068924707,1.8908489612027308,1.9294377155129905,1.9680264698232504,2.00661522413351,2.04520397844377,2.0837927327540298,2.122381487064289,2.1609702413745495,2.199558995684809,2.238147749995069,2.2767365043053287,2.3153252586155886,2.353914012925848,2.3925027672361083,2.4310915215463678,2.4696802758566276,2.5082690301668875,2.5468577844771474,2.5854465387874073,2.624035293097667,2.6626240474079266,2.701212801718187,2.7398015560284463,2.778390310338706,2.816979064648966,2.855567818959226,2.894156573269486,2.9327453275797453,2.9713340818900056,3.0099228362002655,3.048511590510525,3.0871003448207848,3.125689099131044,3.1642778534413045,3.202866607751564,3.241455362061824,3.2800441163720833,3.3186328706823436,3.3572216249926035,3.395810379302863,3.4343991336131228,3.472987887923383,3.5115766422336425,3.5501653965439024,3.588754150854162,3.627342905164422,3.665931659474682,3.7045204137849415,3.7431091680952013,3.7816979224054617,3.820286676715721,3.858875431025981,3.8974641853362404,3.9360529396465007,3.9746416939567606,4.01323044826702,4.0518192025772795,4.09040795688754,4.1289967111978,4.1675854655080595,4.206174219818319,4.244762974128578,4.283351728438839,4.321940482749099,4.3605292370593585,4.399117991369618,4.437706745679878,4.476295499990138,4.514884254300398,4.553473008610657,4.592061762920918,4.630650517231177,4.669239271541437,4.707828025851696,4.746416780161956,4.785005534472217,4.823594288782476,4.8621830430927355,4.900771797402996,4.939360551713255,4.977949306023516,5.016538060333775,5.0551268146440345,5.093715568954295,5.132304323264554,5.1708930775748145,5.209481831885074,5.248070586195334,5.286659340505594,5.325248094815853,5.3638368491261135,5.402425603436374,5.441014357746633,5.479603112056893,5.518191866367152,5.556780620677412,5.595369374987673,5.633958129297932,5.672546883608192,5.711135637918452,5.749724392228711,5.788313146538972,5.826901900849231,5.8654906551594905,5.90407940946975,5.942668163780011,5.981256918090271,6.019845672400531,6.05843442671079,6.09702318102105,6.135611935331309,6.1742006896415695,6.212789443951829,6.251378198262088,6.28996695257235,6.328555706882609,6.3671444611928685,6.405733215503128,6.444321969813388,6.482910724123648,6.521499478433907,6.5600882327441665,6.598676987054428,6.637265741364687,6.6758544956749475,6.714443249985207,6.753032004295466,6.791620758605726,6.830209512915986,6.8687982672262455,6.907387021536507,6.945975775846766,6.984564530157026,7.023153284467285,7.061742038777545,7.100330793087805,7.138919547398064,7.177508301708324,7.216097056018584,7.254685810328844,7.293274564639105,7.331863318949364,7.3704520732596235,7.409040827569883,7.447629581880143,7.486218336190403,7.524807090500662,7.563395844810923,7.601984599121183,7.640573353431442,7.679162107741702,7.717750862051962,7.756339616362221,7.794928370672481,7.833517124982741,7.872105879293001,7.910694633603261,7.949283387913521,7.987872142223781,8.02646089653404,8.0650496508443,8.103638405154559,8.142227159464818,8.18081591377508,8.219404668085339,8.2579934223956,8.29658217670586,8.335170931016119,8.373759685326378,8.412348439636638,8.450937193946897,8.489525948257157,8.528114702567418,8.566703456877677,8.605292211187937,8.643880965498198,8.682469719808457,8.721058474118717,8.759647228428976,8.798235982739236,8.836824737049497,8.875413491359756,8.914002245670016,8.952590999980275,8.991179754290535,9.029768508600796,9.068357262911055,9.106946017221315,9.145534771531574,9.184123525841835,9.222712280152095,9.261301034462354,9.299889788772614,9.338478543082873,9.377067297393133,9.415656051703392,9.454244806013653,9.492833560323913,9.531422314634174,9.570011068944433,9.608599823254693,9.647188577564952,9.685777331875212,9.724366086185471,9.76295484049573,9.801543594805992,9.840132349116251,9.87872110342651,9.917309857736772,9.955898612047031,9.99448736635729,10.03307612066755,10.07166487497781,10.110253629288069,10.14884238359833,10.18743113790859,10.226019892218849,10.264608646529108,10.30319740083937,10.341786155149629,10.380374909459888,10.418963663770148,10.45755241808041,10.496141172390669,10.534729926700928,10.573318681011187,10.611907435321447,10.650496189631706,10.689084943941968,10.727673698252227,10.766262452562486,10.804851206872748,10.843439961183007,10.882028715493266,10.920617469803526,10.959206224113785,10.997794978424045,11.036383732734304,11.074972487044564,11.113561241354825,11.152149995665084,11.190738749975345,11.229327504285605,11.267916258595864,11.306505012906124,11.345093767216383,11.383682521526643,11.422271275836904,11.460860030147163,11.499448784457423,11.538037538767682,11.576626293077943,11.615215047388203,11.653803801698462,11.692392556008722,11.730981310318981,11.76957006462924,11.8081588189395,11.84674757324976,11.885336327560022,11.923925081870282,11.962513836180541,12.0011025904908,12.039691344801062,12.078280099111321,12.11686885342158,12.15545760773184,12.1940463620421,12.232635116352359,12.271223870662618,12.309812624972878,12.348401379283139,12.386990133593399,12.425578887903658,12.464167642213917,12.502756396524177,12.54134515083444,12.5799339051447,12.618522659454959,12.657111413765218,12.695700168075478,12.734288922385737,12.772877676695996,12.811466431006256,12.850055185316517,12.888643939626776,12.927232693937036,12.965821448247295,13.004410202557555,13.042998956867814,13.081587711178074,13.120176465488333,13.158765219798594,13.197353974108855,13.235942728419115,13.274531482729374,13.313120237039636,13.351708991349895,13.390297745660154,13.428886499970414,13.467475254280673,13.506064008590933,13.544652762901192,13.583241517211452,13.621830271521713,13.660419025831972,13.699007780142232,13.737596534452491,13.77618528876275,13.814774043073013,13.853362797383273,13.891951551693532,13.930540306003792,13.969129060314051,14.00771781462431,14.04630656893457,14.08489532324483,14.12348407755509,14.16207283186535,14.20066158617561,14.239250340485869,14.277839094796128,14.316427849106388,14.355016603416647,14.393605357726907,14.432194112037168,14.47078286634743,14.509371620657689,14.547960374967948,14.58654912927821,14.625137883588469,14.663726637898728,14.702315392208988,14.740904146519247,14.779492900829506,14.818081655139766,14.856670409450025,14.895259163760286,14.933847918070546,14.972436672380805,15.011025426691065,15.049614181001324,15.088202935311584,15.126791689621847,15.165380443932106,15.203969198242365,15.242557952552625,15.281146706862884,15.319735461173144,15.358324215483403,15.396912969793664,15.435501724103924,15.474090478414183,15.512679232724443,15.551267987034702,15.589856741344962,15.628445495655221,15.667034249965482,15.705623004275742,15.744211758586003,15.782800512896262,15.821389267206522,15.859978021516783,15.898566775827042,15.937155530137302,15.975744284447561,16.014333038757822,16.05292179306808,16.09151054737834,16.1300993016886,16.16868805599886,16.207276810309118,16.24586556461938,16.284454318929637,16.323043073239898,16.36163182755016,16.40022058186042,16.438809336170678,16.47739809048094,16.5159868447912,16.554575599101458,16.59316435341172,16.631753107721977,16.670341862032238,16.708930616342496,16.747519370652757,16.786108124963018,16.824696879273276,16.863285633583537,16.901874387893795,16.940463142204056,16.979051896514314,17.01764065082458,17.056229405134836,17.094818159445097,17.133406913755355,17.171995668065616,17.210584422375874,17.249173176686135,17.287761930996396,17.326350685306654,17.364939439616915,17.403528193927173,17.442116948237434,17.48070570254769,17.519294456857953,17.55788321116821,17.59647196547847,17.635060719788733,17.673649474098994,17.71223822840925,17.750826982719513,17.789415737029774,17.82800449134003,17.866593245650293,17.90518199996055,17.943770754270812,17.98235950858107,18.02094826289133,18.059537017201592,18.09812577151185,18.13671452582211,18.17530328013237,18.21389203444263,18.252480788752887,18.29106954306315,18.32965829737341,18.36824705168367,18.40683580599393,18.44542456030419,18.484013314614447,18.52260206892471,18.56119082323497,18.599779577545227,18.63836833185549,18.676957086165746,18.715545840476008,18.754134594786265,18.792723349096526,18.831312103406784,18.869900857717045,18.908489612027306,18.947078366337568,18.985667120647825,19.024255874958087,19.062844629268348,19.101433383578605,19.140022137888867,19.178610892199124,19.217199646509386,19.255788400819643,19.294377155129904,19.332965909440166,19.371554663750423,19.410143418060684,19.448732172370942,19.487320926681203,19.52590968099146,19.564498435301722,19.603087189611983,19.641675943922245,19.680264698232502,19.718853452542763,19.75744220685302,19.796030961163282,19.834619715473544,19.8732084697838,19.911797224094062,19.95038597840432,19.98897473271458,20.02756348702484,20.0661522413351,20.10474099564536,20.14332974995562,20.18191850426588,20.220507258576138,20.2590960128864,20.29768476719666,20.33627352150692,20.37486227581718,20.41345103012744,20.452039784437698,20.49062853874796,20.529217293058217,20.567806047368478,20.60639480167874,20.644983555988997,20.683572310299258,20.722161064609516,20.760749818919777,20.799338573230035,20.837927327540296,20.876516081850557,20.91510483616082,20.953693590471076,20.992282344781337,21.030871099091595,21.069459853401856,21.108048607712117,21.146637362022375,21.185226116332636,21.223814870642894,21.262403624953155,21.300992379263413,21.339581133573674,21.378169887883935,21.416758642194193,21.455347396504454,21.49393615081471,21.532524905124973,21.571113659435234,21.609702413745495,21.648291168055753,21.686879922366014,21.72546867667627,21.764057430986533,21.80264618529679,21.84123493960705,21.879823693917313,21.91841244822757,21.957001202537832,21.99558995684809,22.03417871115835,22.07276746546861,22.11135621977887,22.149944974089127,22.188533728399392,22.22712248270965,22.26571123701991,22.30429999133017,22.34288874564043,22.38147749995069,22.42006625426095,22.45865500857121,22.497243762881467,22.53583251719173,22.574421271501986,22.613010025812248,22.65159878012251,22.690187534432766,22.728776288743028,22.767365043053285,22.805953797363546,22.844542551673808,22.88313130598407,22.921720060294327,22.960308814604588,22.998897568914845,23.037486323225107,23.076075077535364,23.114663831845625,23.153252586155887,23.191841340466144,23.230430094776406,23.269018849086663,23.307607603396924,23.346196357707182,23.384785112017443,23.423373866327704,23.461962620637962,23.500551374948223,23.53914012925848,23.577728883568742,23.616317637879,23.65490639218926,23.69349514649952,23.73208390080978,23.770672655120045,23.809261409430306,23.847850163740564,23.886438918050825,23.925027672361082,23.963616426671344,24.0022051809816,24.040793935291862,24.079382689602124,24.11797144391238,24.156560198222643,24.1951489525329,24.23373770684316,24.27232646115342,24.31091521546368,24.349503969773938,24.3880927240842,24.42668147839446,24.465270232704718,24.50385898701498,24.542447741325237,24.581036495635498,24.619625249945756,24.658214004256017,24.696802758566278,24.735391512876536,24.773980267186797,24.812569021497055,24.851157775807316,24.889746530117574,24.928335284427835,24.966924038738092,25.005512793048354,25.04410154735862,25.08269030166888,25.121279055979137,25.1598678102894,25.198456564599656,25.237045318909917,25.275634073220175,25.314222827530436,25.352811581840697,25.391400336150955,25.429989090461216,25.468577844771474,25.507166599081735,25.545755353391993,25.584344107702254,25.62293286201251,25.661521616322773,25.700110370633034,25.73869912494329,25.777287879253553,25.81587663356381,25.854465387874072,25.89305414218433,25.93164289649459,25.970231650804852,26.00882040511511,26.04740915942537,26.08599791373563,26.12458666804589,26.163175422356147,26.20176417666641,26.240352930976666,26.278941685286927,26.31753043959719,26.356119193907453,26.39470794821771,26.433296702527972,26.47188545683823,26.51047421114849,26.54906296545875,26.58765171976901,26.62624047407927,26.66482922838953,26.70341798269979,26.742006737010048,26.78059549132031,26.819184245630566,26.857772999940828,26.896361754251085,26.934950508561347,26.973539262871608,27.012128017181865,27.050716771492127,27.089305525802384,27.127894280112645,27.166483034422903,27.205071788733164,27.243660543043426,27.282249297353683,27.320838051663944,27.359426805974202,27.398015560284463,27.43660431459472,27.475193068904982,27.51378182321524,27.5523705775255,27.590959331835762,27.629548086146027,27.668136840456285,27.706725594766546,27.745314349076803,27.783903103387065,27.822491857697322,27.861080612007584,27.899669366317845,27.938258120628102,27.976846874938364,28.01543562924862,28.054024383558883,28.09261313786914,28.1312018921794,28.16979064648966,28.20837940079992,28.24696815511018,28.28555690942044,28.3241456637307,28.362734418040958,28.40132317235122,28.439911926661477,28.478500680971738,28.517089435282,28.555678189592257,28.594266943902518,28.632855698212776,28.671444452523037,28.710033206833295,28.748621961143556,28.787210715453813,28.825799469764075,28.864388224074336,28.9029769783846,28.94156573269486,28.98015448700512,29.018743241315377,29.05733199562564,29.095920749935896,29.134509504246157,29.17309825855642,29.211687012866676,29.250275767176937,29.288864521487195,29.327453275797456,29.366042030107714,29.404630784417975,29.443219538728233,29.481808293038494,29.520397047348755,29.558985801659013,29.597574555969274,29.63616331027953,29.674752064589793,29.71334081890005,29.75192957321031,29.790518327520573,29.82910708183083,29.867695836141092,29.90628459045135,29.94487334476161,29.98346209907187,30.02205085338213,30.060639607692387,30.09922836200265,30.13781711631291,30.176405870623167,30.214994624933432,30.253583379243693,30.29217213355395,30.330760887864212,30.36934964217447,30.40793839648473,30.446527150794992,30.48511590510525,30.52370465941551,30.56229341372577,30.60088216803603,30.639470922346288,30.67805967665655,30.716648430966806,30.755237185277068,30.79382593958733,30.832414693897586,30.871003448207848,30.909592202518105,30.948180956828367,30.986769711138624,31.025358465448885,31.063947219759147,31.102535974069404,31.141124728379665,31.179713482689923,31.218302237000184,31.256890991310442,31.295479745620703,31.334068499930964,31.372657254241222,31.411246008551483,31.44983476286174,31.488423517172006,31.527012271482267,31.565601025792525,31.604189780102786,31.642778534413043,31.681367288723305,31.719956043033566,31.758544797343824,31.797133551654085,31.835722305964342,31.874311060274604,31.91289981458486,31.951488568895122,31.990077323205384,32.028666077515645,32.0672548318259,32.10584358613616,32.14443234044642,32.18302109475668,32.22160984906694,32.2601986033772,32.29878735768746,32.33737611199772,32.37596486630798,32.414553620618236,32.4531423749285,32.49173112923876,32.53031988354902,32.56890863785927,32.607497392169535,32.646086146479796,32.68467490079006,32.72326365510032,32.76185240941058,32.80044116372084,32.8390299180311,32.877618672341356,32.91620742665162,32.95479618096188,32.99338493527214,33.0319736895824,33.070562443892655,33.109151198202916,33.14773995251318,33.18632870682344,33.22491746113369,33.263506215443954,33.302094969754215,33.340683724064476,33.37927247837474,33.41786123268499,33.45644998699525,33.495038741305514,33.533627495615775,33.572216249926036,33.61080500423629,33.64939375854655,33.68798251285681,33.726571267167074,33.76516002147733,33.80374877578759,33.84233753009785,33.88092628440811,33.91951503871837,33.95810379302863,33.99669254733889,34.03528130164916,34.07387005595941,34.11245881026967,34.15104756457993,34.189636318890194,34.228225073200456,34.26681382751071,34.30540258182097,34.34399133613123,34.38258009044149,34.42116884475175,34.45975759906201,34.49834635337227,34.53693510768253,34.57552386199279,34.614112616303046,34.65270137061331,34.69129012492357,34.72987887923383,34.768467633544084,34.807056387854345,34.84564514216461,34.88423389647487,34.92282265078513,34.96141140509538,35.000000159405644,35.038588913715905,35.07717766802617,35.11576642233642,35.15435517664668,35.19294393095694,35.231532685267204,35.270121439577466,35.30871019388772,35.34729894819799,35.38588770250825,35.4244764568185,35.463065211128765,35.501653965439026,35.54024271974929,35.57883147405955,35.6174202283698,35.65600898268006,35.694597736990325,35.733186491300586,35.77177524561084,35.8103639999211,35.84895275423136,35.887541508541624,35.926130262851885,35.96471901716214,36.0033077714724,36.04189652578266,36.08048528009292,36.119074034403184,36.15766278871344,36.1962515430237,36.23484029733396,36.27342905164422,36.312017805954476,36.35060656026474,36.389195314575,36.42778406888526,36.46637282319552,36.504961577505775,36.543550331816036,36.5821390861263,36.62072784043656,36.65931659474682,36.69790534905708,36.73649410336734,36.7750828576776,36.81367161198786,36.85226036629812,36.89084912060838,36.92943787491864,36.968026629228895,37.006615383539156,37.04520413784942,37.08379289215968,37.12238164646994,37.160970400780194,37.199559155090455,37.238147909400716,37.27673666371098,37.31532541802123,37.35391417233149,37.392502926641754,37.431091680952015,37.469680435262276,37.50826918957253,37.54685794388279,37.58544669819305,37.624035452503314,37.66262420681357,37.70121296112383,37.73980171543409,37.77839046974435,37.81697922405461,37.85556797836487,37.894156732675135,37.9327454869854,37.97133424129565,38.00992299560591,38.04851174991617,38.087100504226434,38.125689258536696,38.16427801284695,38.20286676715721,38.24145552146747,38.28004427577773,38.31863303008799,38.35722178439825,38.39581053870851,38.43439929301877,38.47298804732903,38.511576801639286,38.55016555594955,38.58875431025981,38.62734306457007,38.66593181888033,38.704520573190585,38.74310932750085,38.78169808181111,38.82028683612137,38.85887559043162,38.897464344741884,38.936053099052145,38.97464185336241,39.01323060767267,39.05181936198292,39.09040811629318,39.128996870603444,39.167585624913706,39.20617437922397,39.24476313353423,39.28335188784449,39.32194064215475,39.360529396465004,39.399118150775266,39.43770690508553,39.47629565939579,39.51488441370604,39.5534731680163,39.592061922326565,39.630650676636826,39.66923943094709,39.70782818525734,39.7464169395676,39.785005693877864,39.823594448188125,39.86218320249838,39.90077195680864,39.9393607111189,39.97794946542916,40.016538219739424,40.05512697404968,40.09371572835994,40.1323044826702,40.17089323698046,40.20948199129072,40.24807074560098,40.28665949991124,40.3252482542215,40.36383700853176,40.402425762842014,40.441014517152276,40.479603271462544,40.5181920257728,40.55678078008306,40.59536953439332,40.63395828870358,40.67254704301384,40.7111357973241,40.74972455163436,40.78831330594462,40.82690206025488,40.86549081456514,40.904079568875396,40.94266832318566,40.98125707749592,41.01984583180618,41.058434586116434,41.097023340426695,41.135612094736956,41.17420084904722,41.21278960335748,41.25137835766773,41.289967111977994,41.328555866288255,41.367144620598516,41.40573337490877,41.44432212921903,41.48291088352929,41.521499637839554,41.560088392149815,41.59867714646007,41.63726590077033,41.67585465508059,41.71444340939085,41.753032163701114,41.791620918011375,41.83020967232164,41.8687984266319,41.90738718094215,41.94597593525241,41.984564689562674,42.023153443872935,42.06174219818319,42.10033095249345,42.13891970680371,42.17750846111397,42.216097215424234,42.25468596973449,42.29327472404475,42.33186347835501,42.37045223266527,42.409040986975526,42.44762974128579,42.48621849559605,42.52480724990631,42.56339600421657,42.601984758526825,42.640573512837086,42.67916226714735,42.71775102145761,42.75633977576787,42.794928530078124,42.833517284388385,42.87210603869865,42.91069479300891,42.94928354731916,42.98787230162942,43.02646105593969,43.065049810249945,43.10363856456021,43.14222731887047,43.18081607318073,43.21940482749099,43.257993581801244,43.296582336111506,43.33517109042177,43.37375984473203,43.41234859904229,43.45093735335254,43.489526107662805,43.528114861973066,43.56670361628333,43.60529237059358,43.64388112490384,43.6824698792141,43.721058633524365,43.759647387834626,43.79823614214488,43.83682489645514,43.8754136507654,43.914002405075664,43.95259115938592,43.99117991369618,44.02976866800644,44.0683574223167,44.10694617662696,44.14553493093722,44.18412368524748,44.22271243955774,44.261301193868,44.299889948178254,44.33847870248852,44.377067456798784,44.415656211109045,44.4542449654193,44.49283371972956,44.53142247403982,44.57001122835008,44.60859998266034,44.6471887369706,44.68577749128086,44.72436624559112,44.76295499990138,44.801543754211636,44.8401325085219,44.87872126283216,44.91731001714242,44.955898771452674,44.994487525762935,45.033076280073196,45.07166503438346,45.11025378869372,45.14884254300397,45.187431297314234,45.226020051624495,45.264608805934756,45.30319756024502,45.34178631455527,45.38037506886553,45.418963823175794,45.457552577486055,45.49614133179631,45.53473008610657,45.57331884041683,45.61190759472709,45.650496349037354,45.689085103347615,45.72767385765788,45.76626261196814,45.80485136627839,45.84344012058865,45.882028874898914,45.920617629209175,45.95920638351944,45.99779513782969,46.03638389213995,46.07497264645021,46.113561400760474,46.15215015507073,46.19073890938099,46.22932766369125],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Advanced Rule 5","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470],"y":[0,0.07780061653396322,0.15560123306792645,0.23340184960188964,0.3112024661358529,0.3890030826698161,0.4668036992037793,0.5446043157377426,0.6224049322717058,0.700205548805669,0.7780061653396322,0.8558067818735954,0.9336073984075586,1.0114080149415217,1.0892086314754852,1.167009248009448,1.2448098645434116,1.3226104810773747,1.400411097611338,1.4782117141453013,1.5560123306792644,1.6338129472132277,1.7116135637471908,1.789414180281154,1.8672147968151172,1.9450154133490805,2.0228160298830433,2.1006166464170066,2.1784172629509704,2.2562178794849332,2.334018496018896,2.41181911255286,2.489619729086823,2.5674203456207865,2.6452209621547493,2.7230215786887126,2.800822195222676,2.8786228117566393,2.9564234282906026,3.0342240448245654,3.1120246613585287,3.189825277892492,3.2676258944264553,3.345426510960418,3.4232271274943815,3.501027744028345,3.578828360562308,3.6566289770962714,3.7344295936302343,3.8122302101641976,3.890030826698161,3.9678314432321242,4.045632059766087,4.12343267630005,4.201233292834013,4.279033909367977,4.356834525901941,4.434635142435903,4.5124357589698665,4.590236375503829,4.668036992037792,4.7458376085717555,4.82363822510572,4.901438841639683,4.979239458173646,5.05704007470761,5.134840691241573,5.212641307775536,5.290441924309499,5.368242540843462,5.446043157377425,5.523843773911389,5.601644390445352,5.679445006979315,5.7572456235132785,5.835046240047242,5.912846856581205,5.9906474731151675,6.068448089649131,6.146248706183094,6.2240493227170575,6.301849939251021,6.379650555784984,6.457451172318947,6.535251788852911,6.613052405386874,6.690853021920836,6.7686536384548,6.846454254988763,6.924254871522726,7.00205548805669,7.079856104590653,7.157656721124616,7.23545733765858,7.313257954192543,7.391058570726505,7.468859187260469,7.546659803794432,7.624460420328395,7.7022610368623585,7.780061653396322,7.857862269930285,7.9356628864642484,8.013463502998212,8.091264119532173,8.169064736066137,8.2468653526001,8.324665969134063,8.402466585668027,8.48026720220199,8.558067818735953,8.635868435269916,8.713669051803882,8.791469668337843,8.869270284871806,8.94707090140577,9.024871517939733,9.102672134473696,9.180472751007658,9.258273367541621,9.336073984075584,9.413874600609548,9.491675217143511,9.569475833677474,9.64727645021144,9.725077066745403,9.802877683279366,9.88067829981333,9.958478916347293,10.036279532881256,10.11408014941522,10.191880765949183,10.269681382483146,10.34748199901711,10.425282615551072,10.503083232085036,10.580883848618997,10.65868446515296,10.736485081686924,10.814285698220887,10.89208631475485,10.969886931288814,11.047687547822777,11.12548816435674,11.203288780890704,11.281089397424667,11.35889001395863,11.436690630492594,11.514491247026557,11.59229186356052,11.670092480094484,11.747893096628447,11.82569371316241,11.903494329696374,11.981294946230335,12.059095562764298,12.136896179298262,12.214696795832225,12.292497412366188,12.370298028900152,12.448098645434115,12.525899261968078,12.603699878502042,12.681500495036005,12.759301111569968,12.837101728103931,12.914902344637895,12.992702961171858,13.070503577705821,13.148304194239785,13.226104810773748,13.303905427307711,13.381706043841673,13.459506660375636,13.5373072769096,13.615107893443563,13.692908509977526,13.77070912651149,13.848509743045453,13.926310359579416,14.00411097611338,14.081911592647343,14.159712209181306,14.23751282571527,14.315313442249233,14.393114058783196,14.47091467531716,14.548715291851122,14.626515908385086,14.704316524919049,14.78211714145301,14.859917757986974,14.937718374520937,15.0155189910549,15.093319607588864,15.171120224122827,15.24892084065679,15.326721457190754,15.404522073724717,15.48232269025868,15.560123306792644,15.637923923326607,15.71572453986057,15.793525156394534,15.871325772928497,15.94912638946246,16.026927005996424,16.104727622530387,16.182528239064347,16.26032885559831,16.338129472132273,16.415930088666236,16.4937307052002,16.571531321734163,16.649331938268126,16.72713255480209,16.804933171336053,16.882733787870016,16.96053440440398,17.038335020937943,17.116135637471906,17.19393625400587,17.271736870539833,17.349537487073796,17.427338103607763,17.505138720141726,17.582939336675686,17.66073995320965,17.738540569743613,17.816341186277576,17.89414180281154,17.971942419345503,18.049743035879466,18.12754365241343,18.205344268947393,18.283144885481356,18.360945502015316,18.438746118549282,18.516546735083242,18.59434735161721,18.67214796815117,18.749948584685136,18.827749201219095,18.905549817753062,18.983350434287022,19.06115105082099,19.13895166735495,19.216752283888916,19.29455290042288,19.372353516956842,19.450154133490805,19.52795475002477,19.605755366558732,19.683555983092695,19.76135659962666,19.83915721616062,19.916957832694585,19.994758449228545,20.072559065762512,20.15035968229647,20.22816029883044,20.3059609153644,20.383761531898365,20.461562148432325,20.53936276496629,20.61716338150025,20.69496399803422,20.772764614568178,20.850565231102145,20.928365847636105,21.00616646417007,21.08396708070403,21.161767697237995,21.239568313771958,21.31736893030592,21.395169546839885,21.472970163373848,21.55077077990781,21.628571396441775,21.706372012975738,21.7841726295097,21.861973246043664,21.939773862577628,22.01757447911159,22.095375095645554,22.173175712179518,22.25097632871348,22.328776945247444,22.406577561781408,22.48437817831537,22.562178794849334,22.639979411383294,22.71778002791726,22.79558064445122,22.873381260985187,22.951181877519147,23.028982494053114,23.106783110587074,23.18458372712104,23.262384343655,23.340184960188967,23.417985576722927,23.495786193256894,23.573586809790854,23.65138742632482,23.72918804285878,23.806988659392747,23.884789275926707,23.96258989246067,24.040390508994633,24.118191125528597,24.19599174206256,24.273792358596523,24.351592975130487,24.42939359166445,24.507194208198413,24.584994824732377,24.66279544126634,24.740596057800303,24.818396674334267,24.89619729086823,24.973997907402193,25.051798523936156,25.12959914047012,25.207399757004083,25.285200373538046,25.36300099007201,25.44080160660597,25.518602223139936,25.596402839673896,25.674203456207863,25.752004072741823,25.82980468927579,25.90760530580975,25.985405922343716,26.063206538877676,26.141007155411643,26.218807771945603,26.29660838847957,26.37440900501353,26.452209621547496,26.530010238081456,26.607810854615423,26.685611471149382,26.763412087683346,26.84121270421731,26.919013320751272,26.996813937285236,27.0746145538192,27.152415170353162,27.230215786887126,27.30801640342109,27.385817019955052,27.463617636489015,27.54141825302298,27.619218869556942,27.697019486090905,27.77482010262487,27.852620719158832,27.930421335692795,28.00822195222676,28.086022568760722,28.163823185294685,28.241623801828645,28.319424418362612,28.39722503489657,28.47502565143054,28.552826267964498,28.630626884498465,28.708427501032425,28.78622811756639,28.86402873410035,28.94182935063432,29.019629967168278,29.097430583702245,29.175231200236205,29.25303181677017,29.33083243330413,29.408633049838098,29.486433666372058,29.56423428290602,29.642034899439984,29.719835515973948,29.79763613250791,29.875436749041874,29.953237365575838,30.0310379821098,30.108838598643764,30.186639215177728,30.26443983171169,30.342240448245654,30.420041064779618,30.49784168131358,30.575642297847544,30.653442914381507,30.73124353091547,30.809044147449434,30.886844763983397,30.96464538051736,31.04244599705132,31.120246613585287,31.198047230119247,31.275847846653214,31.353648463187174,31.43144907972114,31.5092496962551,31.587050312789067,31.664850929323027,31.742651545856994,31.820452162390954,31.89825277892492,31.97605339545888,32.05385401199285,32.13165462852681,32.209455245060774,32.28725586159474,32.36505647812869,32.44285709466266,32.52065771119662,32.59845832773059,32.676258944264546,32.75405956079852,32.83186017733247,32.90966079386644,32.9874614104004,33.06526202693437,33.143062643468326,33.2208632600023,33.29866387653625,33.37646449307022,33.45426510960418,33.53206572613815,33.609866342672106,33.68766695920608,33.76546757574003,33.843268192273996,33.92106880880796,33.99886942534192,34.076670041875886,34.15447065840985,34.23227127494381,34.310071891477776,34.38787250801174,34.4656731245457,34.543473741079666,34.62127435761363,34.69907497414759,34.776875590681556,34.854676207215526,34.93247682374948,35.01027744028345,35.08807805681741,35.16587867335137,35.243679289885335,35.3214799064193,35.39928052295326,35.477081139487225,35.55488175602119,35.63268237255515,35.710482989089115,35.78828360562308,35.86608422215704,35.943884838691005,36.02168545522497,36.09948607175893,36.177286688292895,36.25508730482686,36.33288792136082,36.410688537894785,36.48848915442875,36.56628977096271],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"New Buy 5","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864],"y":[0,0.027567933498109425,0.05513586699621885,0.08270380049432827,0.1102717339924377,0.13783966749054713,0.16540760098865653,0.19297553448676596,0.2205434679848754,0.24811140148298483,0.27567933498109426,0.30324726847920364,0.33081520197731307,0.3583831354754225,0.38595106897353193,0.4135190024716414,0.4410869359697508,0.4686548694678602,0.49622280296596966,0.5237907364640791,0.5513586699621885,0.5789266034602979,0.6064945369584073,0.6340624704565168,0.6616304039546261,0.6891983374527356,0.716766270950845,0.7443342044489545,0.7719021379470639,0.7994700714451735,0.8270380049432828,0.8546059384413922,0.8821738719395016,0.9097418054376111,0.9373097389357204,0.9648776724338298,0.9924456059319393,1.020013539430049,1.0475814729281583,1.0751494064262677,1.102717339924377,1.1302852734224864,1.1578532069205958,1.1854211404187052,1.2129890739168145,1.2405570074149241,1.2681249409130335,1.295692874411143,1.3232608079092523,1.3508287414073619,1.3783966749054712,1.4059646084035806,1.43353254190169,1.4611004753997996,1.488668408897909,1.5162363423960186,1.5438042758941277,1.5713722093922373,1.598940142890347,1.626508076388456,1.6540760098865657,1.6816439433846748,1.7092118768827844,1.7367798103808938,1.7643477438790032,1.7919156773771128,1.8194836108752221,1.8470515443733315,1.874619477871441,1.9021874113695503,1.9297553448676596,1.9573232783657692,1.9848912118638786,2.012459145361988,2.040027078860098,2.0675950123582068,2.0951629458563166,2.1227308793544255,2.1502988128525353,2.1778667463506447,2.205434679848754,2.2330026133468635,2.260570546844973,2.288138480343082,2.3157064138411916,2.3432743473393014,2.3708422808374103,2.39841021433552,2.425978147833629,2.453546081331739,2.4811140148298483,2.5086819483279577,2.536249881826067,2.563817815324177,2.591385748822286,2.6189536823203956,2.6465216158185045,2.6740895493166144,2.7016574828147237,2.729225416312833,2.7567933498109425,2.7843612833090523,2.8119292168071612,2.839497150305271,2.86706508380338,2.89463301730149,2.922200950799599,2.9497688842977086,2.977336817795818,3.0049047512939273,3.032472684792037,3.060040618290146,3.0876085517882554,3.115176485286365,3.1427444187844746,3.170312352282584,3.197880285780694,3.2254482192788028,3.253016152776912,3.2805840862750215,3.3081520197731313,3.3357199532712407,3.3632878867693496,3.390855820267459,3.418423753765569,3.445991687263678,3.4735596207617876,3.5011275542598965,3.5286954877580063,3.5562634212561157,3.5838313547542255,3.611399288252335,3.6389672217504443,3.6665351552485532,3.694103088746663,3.7216710222447724,3.749238955742882,3.7768068892409916,3.8043748227391005,3.83194275623721,3.8595106897353193,3.887078623233429,3.9146465567315385,3.9422144902296483,3.9697824237277572,3.9973503572258666,4.024918290723976,4.052486224222085,4.080054157720196,4.107622091218304,4.1351900247164135,4.162757958214523,4.190325891712633,4.2178938252107425,4.245461758708851,4.27302969220696,4.300597625705071,4.32816555920318,4.355733492701289,4.383301426199399,4.410869359697508,4.4384372931956175,4.466005226693727,4.493573160191836,4.521141093689946,4.548709027188055,4.576276960686164,4.603844894184274,4.631412827682383,4.658980761180493,4.686548694678603,4.714116628176711,4.741684561674821,4.769252495172931,4.79682042867104,4.82438836216915,4.851956295667258,4.879524229165368,4.907092162663478,4.934660096161587,4.962228029659697,4.989795963157806,5.017363896655915,5.044931830154025,5.072499763652134,5.100067697150243,5.127635630648354,5.155203564146462,5.182771497644572,5.210339431142682,5.237907364640791,5.265475298138901,5.293043231637009,5.320611165135119,5.348179098633229,5.375747032131338,5.4033149656294475,5.430882899127557,5.458450832625666,5.486018766123776,5.513586699621885,5.541154633119994,5.568722566618105,5.596290500116213,5.6238584336143225,5.651426367112432,5.678994300610542,5.7065622341086515,5.73413016760676,5.761698101104869,5.78926603460298,5.816833968101089,5.844401901599198,5.871969835097308,5.899537768595417,5.9271057020935265,5.954673635591636,5.982241569089744,6.009809502587855,6.037377436085964,6.064945369584074,6.092513303082183,6.120081236580292,6.147649170078402,6.175217103576511,6.202785037074621,6.23035297057273,6.257920904070839,6.285488837568949,6.313056771067058,6.340624704565168,6.368192638063277,6.395760571561388,6.423328505059496,6.4508964385576055,6.478464372055715,6.506032305553824,6.5336002390519345,6.561168172550043,6.588736106048152,6.616304039546263,6.643871973044371,6.671439906542481,6.69900784004059,6.726575773538699,6.7541437070368096,6.781711640534918,6.809279574033028,6.836847507531138,6.864415441029246,6.891983374527356,6.919551308025466,6.947119241523575,6.974687175021685,7.002255108519793,7.029823042017903,7.057390975516013,7.084958909014123,7.112526842512231,7.140094776010342,7.167662709508451,7.19523064300656,7.22279857650467,7.250366510002778,7.277934443500889,7.305502376998998,7.3330703104971064,7.360638243995217,7.388206177493326,7.415774110991436,7.443342044489545,7.470909977987653,7.498477911485764,7.526045844983873,7.553613778481983,7.581181711980092,7.608749645478201,7.636317578976311,7.66388551247442,7.69145344597253,7.719021379470639,7.746589312968748,7.774157246466858,7.801725179964967,7.829293113463077,7.856861046961186,7.884428980459297,7.911996913957405,7.9395648474555145,7.967132780953624,7.994700714451733,8.022268647949844,8.049836581447952,8.07740451494606,8.10497244844417,8.132540381942281,8.160108315440391,8.1876762489385,8.215244182436608,8.242812115934719,8.270380049432827,8.297947982930937,8.325515916429046,8.353083849927156,8.380651783425266,8.408219716923375,8.435787650421485,8.463355583919594,8.490923517417702,8.518491450915812,8.54605938441392,8.573627317912031,8.601195251410141,8.628763184908252,8.65633111840636,8.683899051904469,8.711466985402579,8.739034918900687,8.766602852398798,8.794170785896906,8.821738719395016,8.849306652893127,8.876874586391235,8.904442519889345,8.932010453387454,8.959578386885562,8.987146320383673,9.014714253881781,9.042282187379891,9.069850120878002,9.09741805437611,9.12498598787422,9.152553921372329,9.180121854870439,9.207689788368548,9.235257721866656,9.262825655364766,9.290393588862877,9.317961522360987,9.345529455859095,9.373097389357206,9.400665322855314,9.428233256353423,9.455801189851533,9.483369123349641,9.510937056847752,9.538504990345862,9.56607292384397,9.59364085734208,9.621208790840189,9.6487767243383,9.676344657836408,9.703912591334516,9.731480524832627,9.759048458330737,9.786616391828847,9.814184325326956,9.841752258825064,9.869320192323174,9.896888125821283,9.924456059319393,9.952023992817502,9.979591926315612,10.007159859813722,10.03472779331183,10.06229572680994,10.08986366030805,10.11743159380616,10.144999527304268,10.172567460802378,10.200135394300487,10.227703327798597,10.255271261296707,10.282839194794816,10.310407128292924,10.337975061791035,10.365542995289143,10.393110928787253,10.420678862285364,10.448246795783472,10.475814729281582,10.503382662779691,10.530950596277801,10.55851852977591,10.586086463274018,10.613654396772128,10.641222330270239,10.668790263768347,10.696358197266457,10.723926130764566,10.751494064262676,10.779061997760785,10.806629931258895,10.834197864757003,10.861765798255114,10.889333731753224,10.916901665251332,10.944469598749443,10.972037532247551,10.999605465745661,11.02717339924377,11.054741332741878,11.082309266239989,11.109877199738099,11.13744513323621,11.165013066734318,11.192581000232426,11.220148933730536,11.247716867228645,11.275284800726755,11.302852734224864,11.330420667722974,11.357988601221084,11.385556534719193,11.413124468217303,11.440692401715411,11.46826033521352,11.49582826871163,11.523396202209739,11.550964135707849,11.57853206920596,11.60610000270407,11.633667936202178,11.661235869700286,11.688803803198397,11.716371736696505,11.743939670194615,11.771507603692724,11.799075537190834,11.826643470688945,11.854211404187053,11.881779337685163,11.909347271183272,11.936915204681382,11.964483138179489,11.992051071677599,12.01961900517571,12.04718693867382,12.074754872171928,12.102322805670038,12.129890739168149,12.157458672666255,12.185026606164366,12.212594539662476,12.240162473160584,12.267730406658695,12.295298340156805,12.322866273654915,12.350434207153022,12.378002140651132,12.405570074149242,12.433138007647349,12.46070594114546,12.48827387464357,12.515841808141678,12.543409741639788,12.570977675137899,12.598545608636009,12.626113542134116,12.653681475632226,12.681249409130336,12.708817342628445,12.736385276126555,12.763953209624665,12.791521143122775,12.819089076620882,12.846657010118992,12.874224943617103,12.901792877115211,12.92936081061332,12.95692874411143,12.984496677609538,13.012064611107649,13.039632544605759,13.067200478103869,13.094768411601976,13.122336345100086,13.149904278598196,13.177472212096305,13.205040145594415,13.232608079092525,13.260176012590632,13.287743946088742,13.315311879586853,13.342879813084963,13.370447746583071,13.39801568008118,13.42558361357929,13.453151547077399,13.480719480575509,13.508287414073619,13.53585534757173,13.563423281069836,13.590991214567946,13.618559148066057,13.646127081564165,13.673695015062275,13.701262948560386,13.728830882058492,13.756398815556603,13.783966749054713,13.811534682552823,13.839102616050932,13.866670549549042,13.89423848304715,13.921806416545259,13.94937435004337,13.97694228354148,14.004510217039586,14.032078150537696,14.059646084035807,14.087214017533917,14.114781951032025,14.142349884530136,14.169917818028246,14.197485751526353,14.225053685024463,14.252621618522573,14.280189552020683,14.307757485518792,14.335325419016902,14.362893352515012,14.39046128601312,14.41802921951123,14.44559715300934,14.473165086507446,14.500733020005557,14.528300953503667,14.555868887001777,14.583436820499886,14.611004753997996,14.638572687496106,14.666140620994213,14.693708554492323,14.721276487990433,14.748844421488542,14.776412354986652,14.803980288484762,14.831548221982873,14.85911615548098,14.88668408897909,14.9142520224772,14.941819955975307,14.969387889473417,14.996955822971527,15.024523756469637,15.052091689967746,15.079659623465856,15.107227556963966,15.134795490462073,15.162363423960183,15.189931357458294,15.217499290956402,15.245067224454512,15.272635157952623,15.300203091450733,15.32777102494884,15.35533895844695,15.38290689194506,15.410474825443167,15.438042758941277,15.465610692439387,15.493178625937496,15.520746559435606,15.548314492933716,15.575882426431827,15.603450359929933,15.631018293428044,15.658586226926154,15.686154160424262,15.713722093922373,15.741290027420483,15.768857960918593,15.7964258944167,15.82399382791481,15.85156176141292,15.879129694911029,15.906697628409137,15.934265561907248,15.961833495405356,15.989401428903466,16.016969362401575,16.044537295899687,16.072105229397796,16.099673162895904,16.127241096394016,16.15480902989212,16.182376963390233,16.20994489688834,16.23751283038645,16.265080763884562,16.29264869738267,16.320216630880783,16.347784564378887,16.375352497877,16.402920431375108,16.430488364873217,16.458056298371325,16.485624231869437,16.513192165367546,16.540760098865654,16.568328032363766,16.595895965861875,16.623463899359983,16.65103183285809,16.678599766356204,16.706167699854312,16.73373563335242,16.761303566850533,16.78887150034864,16.81643943384675,16.844007367344858,16.87157530084297,16.899143234341075,16.926711167839187,16.954279101337296,16.981847034835404,17.009414968333516,17.036982901831625,17.064550835329737,17.09211876882784,17.119686702325954,17.147254635824062,17.17482256932217,17.202390502820283,17.22995843631839,17.257526369816503,17.285094303314608,17.31266223681272,17.34023017031083,17.367798103808937,17.395366037307046,17.422933970805158,17.450501904303266,17.478069837801375,17.505637771299487,17.533205704797595,17.560773638295704,17.588341571793812,17.615909505291924,17.643477438790033,17.67104537228814,17.698613305786253,17.726181239284358,17.75374917278247,17.78131710628058,17.80888503977869,17.8364529732768,17.864020906774908,17.891588840273016,17.919156773771125,17.946724707269237,17.974292640767345,18.001860574265457,18.029428507763562,18.056996441261674,18.084564374759783,18.11213230825789,18.139700241756003,18.16726817525411,18.19483610875222,18.22240404225033,18.24997197574844,18.27753990924655,18.305107842744658,18.33267577624277,18.360243709740878,18.387811643238987,18.415379576737095,18.442947510235207,18.470515443733312,18.498083377231424,18.525651310729533,18.553219244227645,18.580787177725753,18.60835511122386,18.635923044721974,18.66349097822008,18.69105891171819,18.7186268452163,18.74619477871441,18.77376271221252,18.801330645710628,18.82889857920874,18.856466512706845,18.884034446204957,18.911602379703066,18.939170313201174,18.966738246699283,18.994306180197395,19.021874113695503,19.04944204719361,19.077009980691724,19.104577914189832,19.13214584768794,19.15971378118605,19.18728171468416,19.21484964818227,19.242417581680378,19.26998551517849,19.2975534486766,19.325121382174707,19.352689315672816,19.380257249170928,19.407825182669033,19.435393116167145,19.462961049665253,19.490528983163365,19.518096916661474,19.545664850159582,19.573232783657694,19.6008007171558,19.62836865065391,19.65593658415202,19.683504517650128,19.71107245114824,19.73864038464635,19.76620831814446,19.793776251642566,19.821344185140678,19.848912118638786,19.876480052136895,19.904047985635003,19.931615919133115,19.959183852631224,19.986751786129332,20.014319719627444,20.041887653125553,20.06945558662366,20.09702352012177,20.12459145361988,20.15215938711799,20.1797273206161,20.20729525411421,20.23486318761232,20.262431121110428,20.289999054608536,20.31756698810665,20.345134921604757,20.372702855102865,20.400270788600974,20.427838722099082,20.455406655597194,20.482974589095303,20.510542522593415,20.53811045609152,20.565678389589632,20.59324632308774,20.62081425658585,20.64838219008396,20.67595012358207,20.703518057080178,20.731085990578286,20.7586539240764,20.786221857574507,20.813789791072615,20.841357724570727,20.868925658068836,20.896493591566944,20.924061525065053,20.951629458563165,20.979197392061273,21.006765325559382,21.03433325905749,21.061901192555602,21.08946912605371,21.11703705955182,21.14460499304993,21.172172926548036,21.19974086004615,21.227308793544257,21.25487672704237,21.282444660540477,21.310012594038586,21.337580527536694,21.365148461034803,21.392716394532915,21.420284328031023,21.447852261529132,21.47542019502724,21.502988128525352,21.53055606202346,21.55812399552157,21.58569192901968,21.61325986251779,21.6408277960159,21.668395729514007,21.69596366301212,21.723531596510227,21.751099530008336,21.778667463506448,21.806235397004556,21.833803330502665,21.861371264000773,21.888939197498885,21.91650713099699,21.944075064495102,21.97164299799321,21.999210931491323,22.02677886498943,22.05434679848754,22.081914731985652,22.109482665483757,22.13705059898187,22.164618532479977,22.192186465978086,22.219754399476198,22.247322332974306,22.27489026647242,22.302458199970523,22.330026133468635,22.357594066966744,22.385162000464852,22.41272993396296,22.440297867461073,22.46786580095918,22.49543373445729,22.523001667955402,22.55056960145351,22.57813753495162,22.605705468449727,22.63327340194784,22.660841335445948,22.688409268944056,22.71597720244217,22.743545135940277,22.771113069438385,22.798681002936494,22.826248936434606,22.85381686993271,22.881384803430823,22.90895273692893,22.93652067042704,22.964088603925152,22.99165653742326,23.019224470921372,23.046792404419477,23.07436033791759,23.101928271415698,23.129496204913806,23.15706413841192,23.184632071910027,23.21220000540814,23.239767938906244,23.267335872404356,23.294903805902464,23.322471739400573,23.35003967289868,23.377607606396793,23.405175539894902,23.43274347339301,23.460311406891122,23.48787934038923,23.51544727388734,23.543015207385448,23.57058314088356,23.59815107438167,23.625719007879777,23.65328694137789,23.680854874875994,23.708422808374106,23.735990741872214,23.763558675370327,23.791126608868435,23.818694542366543],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Prof Rule 10: Hull MA Trend</td>
                <td>PROFESSIONAL</td>
                <td class="positive">58.93%</td>
                <td>63.6%</td>
                <td>1147</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>20.92%</td>
                <td>72.6</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Professional Rule 1: Ichimoku Cloud Breakout</td>
                <td>UNKNOWN</td>
                <td class="positive">56.95%</td>
                <td>63.6%</td>
                <td>975</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>27.24%</td>
                <td>71.9</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Rule 9: EMA Alignment</td>
                <td>ORIGINAL</td>
                <td class="positive">46.23%</td>
                <td>63.2%</td>
                <td>1198</td>
                <td>1.06</td>
                <td>0.00</td>
                <td>20.13%</td>
                <td>67.4</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Advanced Rule 5: Donchian Channel Breakout</td>
                <td>UNKNOWN</td>
                <td class="positive">36.57%</td>
                <td>64.3%</td>
                <td>470</td>
                <td>1.12</td>
                <td>0.00</td>
                <td>24.14%</td>
                <td>63.9</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>New Buy 5: CMF Positive</td>
                <td>UNKNOWN</td>
                <td class="positive">23.82%</td>
                <td>62.7%</td>
                <td>864</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>32.26%</td>
                <td>58.3</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">21.85%</td>
                <td>65.2%</td>
                <td>230</td>
                <td>1.15</td>
                <td>0.00</td>
                <td>8.82%</td>
                <td>58.3</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>AI Rule 3: Smart Money Flow Divergence</td>
                <td>AI_GENERATED</td>
                <td class="positive">17.46%</td>
                <td>64.3%</td>
                <td>311</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>28.83%</td>
                <td>56.3</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Volatility Rule 2: ATR Expansion Signal</td>
                <td>UNKNOWN</td>
                <td class="positive">10.98%</td>
                <td>64.5%</td>
                <td>121</td>
                <td>1.13</td>
                <td>0.00</td>
                <td>12.87%</td>
                <td>53.7</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>AI Rule 6: Market Structure Shift</td>
                <td>AI_GENERATED</td>
                <td class="positive">10.92%</td>
                <td>62.7%</td>
                <td>391</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>24.61%</td>
                <td>53.2</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>Rule 27: Structure Break Up</td>
                <td>ORIGINAL</td>
                <td class="positive">10.00%</td>
                <td>62.6%</td>
                <td>171</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>9.74%</td>
                <td>52.8</td>
            </tr>
            
            <tr>
                <td>11</td>
                <td>Rule 28: Volume Breakout</td>
                <td>ORIGINAL</td>
                <td class="positive">12.49%</td>
                <td>64.2%</td>
                <td>95</td>
                <td>1.19</td>
                <td>0.00</td>
                <td>8.01%</td>
                <td>52.8</td>
            </tr>
            
            <tr>
                <td>12</td>
                <td>Rule 22: Higher High Pattern</td>
                <td>ORIGINAL</td>
                <td class="positive">8.74%</td>
                <td>63.2%</td>
                <td>280</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>24.08%</td>
                <td>52.5</td>
            </tr>
            
            <tr>
                <td>13</td>
                <td>AI Rule 8: Momentum Divergence Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">11.06%</td>
                <td>67.0%</td>
                <td>88</td>
                <td>1.20</td>
                <td>0.00</td>
                <td>9.01%</td>
                <td>50.9</td>
            </tr>
            
            <tr>
                <td>14</td>
                <td>Ext Rule 5: ATR Volatility Expansion</td>
                <td>UNKNOWN</td>
                <td class="positive">4.21%</td>
                <td>63.2%</td>
                <td>133</td>
                <td>1.05</td>
                <td>0.00</td>
                <td>15.90%</td>
                <td>50.6</td>
            </tr>
            
            <tr>
                <td>15</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">2.21%</td>
                <td>63.4%</td>
                <td>634</td>
                <td>1.00</td>
                <td>0.00</td>
                <td>33.46%</td>
                <td>49.9</td>
            </tr>
            
            <tr>
                <td>16</td>
                <td>AI Rule 1: Multi-Timeframe Momentum</td>
                <td>AI_GENERATED</td>
                <td class="positive">15.16%</td>
                <td>74.4%</td>
                <td>43</td>
                <td>1.68</td>
                <td>0.00</td>
                <td>4.07%</td>
                <td>41.3</td>
            </tr>
            
            <tr>
                <td>17</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">8.30%</td>
                <td>67.3%</td>
                <td>52</td>
                <td>1.27</td>
                <td>0.00</td>
                <td>6.88%</td>
                <td>39.1</td>
            </tr>
            
            <tr>
                <td>18</td>
                <td>Professional Rule 5: Bollinger Band Squeeze</td>
                <td>UNKNOWN</td>
                <td class="positive">3.63%</td>
                <td>65.5%</td>
                <td>58</td>
                <td>1.10</td>
                <td>0.00</td>
                <td>8.24%</td>
                <td>38.5</td>
            </tr>
            
            <tr>
                <td>19</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">12.49%</td>
                <td>75.8%</td>
                <td>33</td>
                <td>1.86</td>
                <td>0.00</td>
                <td>6.44%</td>
                <td>37.6</td>
            </tr>
            
            <tr>
                <td>20</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">4.73%</td>
                <td>64.4%</td>
                <td>45</td>
                <td>1.17</td>
                <td>0.00</td>
                <td>6.64%</td>
                <td>34.7</td>
            </tr>
            
            <tr>
                <td>21</td>
                <td>Professional Rule 10: CCI Reversal Enhanced</td>
                <td>UNKNOWN</td>
                <td class="positive">6.50%</td>
                <td>71.4%</td>
                <td>28</td>
                <td>1.39</td>
                <td>0.00</td>
                <td>6.28%</td>
                <td>32.4</td>
            </tr>
            
            <tr>
                <td>22</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">2.69%</td>
                <td>66.7%</td>
                <td>27</td>
                <td>1.14</td>
                <td>0.00</td>
                <td>7.44%</td>
                <td>29.2</td>
            </tr>
            
            <tr>
                <td>23</td>
                <td>Rule 2: Golden Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">4.10%</td>
                <td>71.4%</td>
                <td>14</td>
                <td>1.56</td>
                <td>0.00</td>
                <td>2.25%</td>
                <td>27.3</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 525,300<br>
            • Backtest Range: 300 to 525,600<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
